import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/package_model.dart';
import '../../utils/app_colors.dart';
import '../../widgets/modern_card.dart';
import '../../widgets/modern_button.dart';

class ModernPackagesScreen extends StatefulWidget {
  const ModernPackagesScreen({super.key});

  @override
  State<ModernPackagesScreen> createState() => _ModernPackagesScreenState();
}

class _ModernPackagesScreenState extends State<ModernPackagesScreen> {
  List<PackageModel> _packages = [];
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadPackages();
  }

  Future<void> _loadPackages() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final packages = await authProvider.apiService.getPackages();

      if (mounted) {
        setState(() {
          _packages = packages;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _packages = [];
          _error = 'فشل في تحميل الباقات. تحقق من الاتصال بالإنترنت.';
          _isLoading = false;
        });
      }
    }
  }





  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: isDark ? AppColors.darkBackground : AppColors.neutral50,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          title: Text(
            'الباقات المتاحة',
            style: GoogleFonts.alexandria(
              fontWeight: FontWeight.bold,
              color: isDark ? AppColors.textDarkPrimary : AppColors.textPrimary,
            ),
          ),
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: isDark ? AppColors.textDarkPrimary : AppColors.textPrimary,
            ),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: RefreshIndicator(
          onRefresh: _loadPackages,
          child: _buildBody(),
        ),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryBlue),
        ),
      );
    }

    if (_packages.isEmpty && _error != null) {
      return _buildErrorState();
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_error != null) _buildErrorBanner(),
          
          const SizedBox(height: 16),
          
          // Packages Grid
          ...List.generate(_packages.length, (index) {
            final package = _packages[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: _buildPackageCard(package),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: ModernCard(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.wifi_off,
                size: 64,
                color: isDark ? AppColors.textDarkSecondary : AppColors.textSecondary,
              ),
              const SizedBox(height: 16),
              Text(
                'لا توجد باقات متاحة',
                style: GoogleFonts.alexandria(
                  fontWeight: FontWeight.bold,
                  color: isDark ? AppColors.textDarkPrimary : AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'تحقق من الاتصال بالإنترنت وحاول مرة أخرى',
                style: GoogleFonts.alexandria(
                  color: isDark ? AppColors.textDarkSecondary : AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              ModernButton(
                text: 'إعادة المحاولة',
                onPressed: _loadPackages,
                type: ModernButtonType.primary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorBanner() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.warning.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.info_outline,
            color: AppColors.warning,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _error!,
              style: GoogleFonts.alexandria(
                color: AppColors.warning,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPackageCard(PackageModel package) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return ModernCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          package.name,
                          style: GoogleFonts.alexandria(
                            fontWeight: FontWeight.bold,
                            color: isDark ? AppColors.textDarkPrimary : AppColors.textPrimary,
                          ),
                        ),
                        if (package.isPopular) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: AppColors.warning,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'الأكثر شعبية',
                              style: GoogleFonts.alexandria(
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      package.description ?? 'وصف الباقة',
                      style: GoogleFonts.alexandria(
                        color: isDark ? AppColors.textDarkSecondary : AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                package.priceDisplay,
                style: GoogleFonts.alexandria(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primaryBlue,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Features
          Row(
            children: [
              Expanded(
                child: _buildFeatureItem(
                  icon: Icons.download,
                  label: 'التحميل',
                  value: '${package.downloadSpeed} Mbps',
                ),
              ),
              Expanded(
                child: _buildFeatureItem(
                  icon: Icons.upload,
                  label: 'الرفع',
                  value: '${package.uploadSpeed} Mbps',
                ),
              ),
              Expanded(
                child: _buildFeatureItem(
                  icon: Icons.data_usage,
                  label: 'البيانات',
                  value: package.dataLimit != null ? '${package.dataLimit} GB' : 'غير محدود',
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Action Button
          ModernButton(
            text: 'اختيار الباقة',
            onPressed: () => _selectPackage(package),
            type: package.isPopular ? ModernButtonType.primary : ModernButtonType.outline,
            isFullWidth: true,
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Column(
      children: [
        Icon(
          icon,
          color: AppColors.primaryBlue,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: GoogleFonts.alexandria(
            fontWeight: FontWeight.w600,
            color: isDark ? AppColors.textDarkPrimary : AppColors.textPrimary,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.alexandria(
            color: isDark ? AppColors.textDarkSecondary : AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  void _selectPackage(PackageModel package) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).brightness == Brightness.dark
            ? AppColors.darkCard
            : Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Text(
          'تأكيد اختيار الباقة',
          style: GoogleFonts.alexandria(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'هل تريد اختيار باقة "${package.name}" بسعر ${package.priceDisplay}؟',
          style: GoogleFonts.alexandria(),
        ),
        actions: [
          ModernButton(
            text: 'إلغاء',
            onPressed: () => Navigator.pop(context),
            type: ModernButtonType.text,
          ),
          ModernButton(
            text: 'تأكيد',
            onPressed: () {
              Navigator.pop(context);
              _confirmPackageSelection(package);
            },
            type: ModernButtonType.primary,
          ),
        ],
      ),
    );
  }

  Future<void> _confirmPackageSelection(PackageModel package) async {
    try {
      print('🔄 بدء تغيير الباقة إلى: ${package.name} (ID: ${package.id})');
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? AppColors.darkCard
              : Colors.white,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                'جاري تغيير الباقة...',
                style: GoogleFonts.alexandria(),
              ),
            ],
          ),
        ),
      );

      print('📡 استدعاء API لتغيير الباقة...');
      // Call API to change service
      await authProvider.apiService.changeService(
        package.id.toString(),
        '', // Password might be required by API
      );

      print('✅ تم تغيير الباقة بنجاح في API');

      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم تغيير الباقة إلى "${package.name}" بنجاح',
              style: GoogleFonts.alexandria(color: Colors.white),
            ),
            backgroundColor: AppColors.success,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        );

        print('🔄 إعادة تحميل الباقات...');
        // Reload packages to update current selection
        await _loadPackages();

        print('🔄 تحديث بيانات المستخدم...');
        // Refresh user data to update dashboard
        await authProvider.refreshUserData();

        print('✅ تم تحديث جميع البيانات');
      }
    } catch (e) {
      print('❌ خطأ في تغيير الباقة: $e');

      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في تغيير الباقة: ${e.toString()}',
              style: GoogleFonts.alexandria(color: Colors.white),
            ),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        );
      }
    }
  }
}
