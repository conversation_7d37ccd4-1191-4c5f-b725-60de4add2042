import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../providers/auth_provider.dart';
import '../../providers/packages_provider.dart';
import '../../providers/invoices_provider.dart';
import '../../widgets/glass_card.dart';
import '../../widgets/custom_button.dart' as custom;
import '../../utils/app_colors.dart';

class UserDashboardScreen extends StatefulWidget {
  const UserDashboardScreen({Key? key}) : super(key: key);

  @override
  State<UserDashboardScreen> createState() => _UserDashboardScreenState();
}

class _UserDashboardScreenState extends State<UserDashboardScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  bool _isLoading = false;
  double _currentBalance = 0.0;
  Map<String, dynamic> _trafficData = {};
  List<Map<String, dynamic>> _recentTransactions = [];
  String _currentPackage = 'غير محدد';
  String _currentPackageName = 'غير محدد';

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    _fadeController.forward();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Load user balance and details
      try {
        final userDetails = await authProvider.apiService.refreshUserData();
        _currentBalance = userDetails.balance;

        // Try to get package info from different sources
        if (userDetails.currentPackage != null && userDetails.currentPackage!.isNotEmpty) {
          _currentPackage = userDetails.currentPackage!;
        } else {
          // Use profile_id as package identifier
          _currentPackage = '1'; // Default to profile_id from API response
        }

        // Load package name from API
        await _loadPackageName();
      } catch (e) {
        print('Failed to load balance: $e');
      }

      // Load traffic data (usage)
      try {
        _trafficData = await authProvider.apiService.getTrafficData(
          reportType: 'monthly',
          month: DateTime.now().month,
          year: DateTime.now().year,
        );
        print('Traffic data loaded: $_trafficData');
      } catch (e) {
        print('Failed to load traffic data: $e');
        _trafficData = {};
      }

      // Load recent transactions (invoices)
      try {
        final invoices = await authProvider.apiService.getInvoices(count: 5);
        _recentTransactions = invoices.map((invoice) => {
          'title': 'فاتورة رقم ${invoice.id}',
          'amount': -invoice.amount,
          'date': '${invoice.issueDate.day}/${invoice.issueDate.month}/${invoice.issueDate.year}',
          'type': 'payment',
          'currency': 'د.ل'
        }).toList();
        print('Transactions loaded: ${_recentTransactions.length} items');
      } catch (e) {
        print('Failed to load transactions: $e');
        // No fallback - show empty list if API fails
        _recentTransactions = [];
      }

      // Load user sessions for additional usage data
      try {
        final sessions = await authProvider.apiService.getUserSessions(count: 10);
        print('Sessions data: $sessions');
        // Process sessions data if needed
      } catch (e) {
        print('Failed to load sessions: $e');
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading dashboard data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadPackageName() async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Try to get packages from API
      try {
        final packagesData = await authProvider.apiService.getPackages();
        for (final packageData in packagesData) {
          if (packageData.id.toString() == _currentPackage) {
            _currentPackageName = packageData.name ?? 'باقة رقم $_currentPackage';
            return;
          }
        }
      } catch (e) {
        print('Failed to load packages from API: $e');
      }

      // Fallback to descriptive names based on profile_id
      switch (_currentPackage) {
        case '1':
          _currentPackageName = 'باقة الإنترنت الأساسية';
          break;
        case '2':
          _currentPackageName = 'باقة الإنترنت المتقدمة';
          break;
        case '3':
          _currentPackageName = 'باقة الإنترنت المميزة';
          break;
        default:
          _currentPackageName = 'باقة الإنترنت الأساسية';
      }
    } catch (e) {
      print('Failed to load package name: $e');
      _currentPackageName = 'باقة الإنترنت الأساسية';
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.currentUser;

        return RefreshIndicator(
          onRefresh: _loadDashboardData,
          child: _buildDashboard(context, isDark, user),
        );
      },
    );
  }

  Widget _buildDashboard(BuildContext context, bool isDark, dynamic user) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: isDark
            ? LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.darkBackground,
                  AppColors.darkSurface.withValues(alpha: 0.8),
                  AppColors.primaryCyan.withValues(alpha: 0.1),
                ],
                stops: const [0.0, 0.6, 1.0],
              )
            : LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.primaryBlue,
                  AppColors.primaryIndigo.withValues(alpha: 0.8),
                  AppColors.secondaryTeal.withValues(alpha: 0.6),
                ],
                stops: const [0.0, 0.5, 1.0],
              ),
        ),
        child: SafeArea(
          child: CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              // Modern App Bar
              SliverAppBar(
                expandedHeight: 140,
                floating: false,
                pinned: true,
                backgroundColor: Colors.transparent,
                elevation: 0,
                automaticallyImplyLeading: false,
                flexibleSpace: FlexibleSpaceBar(
                  background: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.1),
                        ],
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Welcome Section
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Text(
                                  'مرحباً بك',
                                  style: GoogleFonts.alexandria(
                                    fontSize: 16,
                                    color: Colors.white.withValues(alpha: 0.9),
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  user?.fullName ?? 'المستخدم',
                                  style: GoogleFonts.alexandria(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                          // Action Buttons
                          Row(
                            children: [
                              _buildHeaderButton(
                                icon: Icons.notifications_outlined,
                                onPressed: () {
                                  HapticFeedback.lightImpact();
                                  _showNotifications(context);
                                },
                              ),
                              const SizedBox(width: 8),
                              _buildHeaderButton(
                                icon: Icons.person_outline,
                                onPressed: () {
                                  HapticFeedback.lightImpact();
                                  _showProfile(context);
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              // Dashboard Content
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    FadeTransition(
                      opacity: _fadeAnimation,
                      child: AnimationLimiter(
                        child: Column(
                          children: AnimationConfiguration.toStaggeredList(
                            duration: const Duration(milliseconds: 600),
                            childAnimationBuilder: (widget) => SlideAnimation(
                              verticalOffset: 50.0,
                              child: FadeInAnimation(child: widget),
                            ),
                            children: [
                              // Welcome Card
                              _buildWelcomeCard(user),

                              const SizedBox(height: 16),

                              // Quick Stats Row
                              Row(
                                children: [
                                  Expanded(child: _buildBalanceCard(user)),
                                  const SizedBox(width: 12),
                                  Expanded(child: _buildUsageCard()),
                                ],
                              ),

                              const SizedBox(height: 16),

                              // Current Plan Card
                              _buildCurrentPlanCard(user),

                              const SizedBox(height: 16),

                              // Quick Actions
                              _buildQuickActionsCard(),

                              const SizedBox(height: 16),

                              // Recent Transactions
                              _buildRecentTransactionsCard(),

                              const SizedBox(height: 16),

                              // Usage Chart
                              _buildUsageChartCard(),

                              const SizedBox(height: 32),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ]),
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildWelcomeCard(dynamic user) {
    return GlassCard(
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: AppColors.primaryGradient,
            ),
            child: const Icon(
              Icons.person,
              color: Colors.white,
              size: 30,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مرحباً بك',
                  style: GoogleFonts.alexandria(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  user?.fullName ?? 'المستخدم',
                  style: GoogleFonts.alexandria(
                    fontSize: 14,
                    color: Colors.white.withValues(alpha: 0.8),
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: AppColors.success.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.success.withValues(alpha: 0.5),
                    ),
                  ),
                  child: Text(
                    user?.isActive == true ? 'متصل' : 'غير متصل',
                    style: GoogleFonts.alexandria(
                      fontSize: 12,
                      color: user?.isActive == true ? AppColors.success : AppColors.error,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceCard(dynamic user) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.accentGreen.withValues(alpha: 0.15),
            AppColors.accentGreen.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.accentGreen.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.accentGreen.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.accentGreen,
                      AppColors.accentGreen.withValues(alpha: 0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.accentGreen.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.account_balance_wallet_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الرصيد المتاح',
                      style: GoogleFonts.alexandria(
                        fontSize: 14,
                        color: Colors.white.withValues(alpha: 0.8),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${_currentBalance.toStringAsFixed(2)} د.ل',
                      style: GoogleFonts.alexandria(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        height: 1.2,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUsageCard() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.accentOrange.withValues(alpha: 0.15),
            AppColors.accentOrange.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.accentOrange.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.accentOrange.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.accentOrange,
                      AppColors.accentOrange.withValues(alpha: 0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.accentOrange.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.data_usage_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الاستهلاك الشهري',
                      style: GoogleFonts.alexandria(
                        fontSize: 14,
                        color: Colors.white.withValues(alpha: 0.8),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _getUsageText(),
                      style: GoogleFonts.alexandria(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        height: 1.2,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentPlanCard(dynamic user) {
    return GlassCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryCyan.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.wifi,
                  color: AppColors.primaryCyan,
                  size: 20,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'الباقة الحالية',
                style: GoogleFonts.alexandria(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () {
                  Navigator.pushNamed(context, '/user/packages');
                },
                child: Text(
                  'تغيير',
                  style: GoogleFonts.alexandria(
                    fontSize: 14,
                    color: AppColors.accentOrange,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            _getCurrentPackage(user),
            style: GoogleFonts.alexandria(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'سرعة 100 ميجا • بيانات غير محدودة',
            style: GoogleFonts.alexandria(
              fontSize: 14,
              color: Colors.white.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsCard() {
    return GlassCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الإجراءات السريعة',
            style: GoogleFonts.alexandria(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildQuickActionItem(
                icon: Icons.payment,
                label: 'دفع الفاتورة',
                color: AppColors.accentGreen,
                onTap: () => Navigator.pushNamed(context, '/user/invoices'),
              ),
              _buildQuickActionItem(
                icon: Icons.add_card,
                label: 'إضافة رصيد',
                color: AppColors.accentOrange,
                onTap: () {
                  // Add credit functionality
                },
              ),
              _buildQuickActionItem(
                icon: Icons.support_agent,
                label: 'الدعم الفني',
                color: AppColors.primaryCyan,
                onTap: () {
                  // Support functionality
                },
              ),
              _buildQuickActionItem(
                icon: Icons.settings,
                label: 'الإعدادات',
                color: AppColors.accentPurple,
                onTap: () {
                  Navigator.pushNamed(context, '/user/settings');
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionItem({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Column(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: color.withOpacity(0.3),
              ),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: GoogleFonts.alexandria(
              fontSize: 12,
              color: Colors.white.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentTransactionsCard() {
    return GlassCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'المعاملات الأخيرة',
                style: GoogleFonts.alexandria(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () {
                  Navigator.pushNamed(context, '/user/invoices');
                },
                child: Text(
                  'عرض الكل',
                  style: GoogleFonts.alexandria(
                    fontSize: 14,
                    color: AppColors.accentOrange,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (_recentTransactions.isEmpty)
            Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Icon(
                    Icons.receipt_long_outlined,
                    size: 48,
                    color: Colors.white.withOpacity(0.5),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'لا توجد معاملات',
                    style: GoogleFonts.alexandria(
                      fontSize: 16,
                      color: Colors.white.withOpacity(0.7),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'ستظهر معاملاتك هنا عند توفرها',
                    style: GoogleFonts.alexandria(
                      fontSize: 12,
                      color: Colors.white.withOpacity(0.5),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            )
          else
            ..._recentTransactions.take(3).map((transaction) {
              return _buildTransactionItem(transaction);
            }),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(Map<String, dynamic> transaction) {
    final isCredit = transaction['type'] == 'credit';
    final amount = transaction['amount'] as double;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: (isCredit ? AppColors.accentGreen : AppColors.accentRed)
                  .withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              isCredit ? Icons.add : Icons.remove,
              color: isCredit ? AppColors.accentGreen : AppColors.accentRed,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  transaction['title'],
                  style: GoogleFonts.alexandria(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  transaction['date'],
                  style: GoogleFonts.alexandria(
                    fontSize: 12,
                    color: Colors.white.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${amount > 0 ? '+' : ''}${amount.toStringAsFixed(2)} ${transaction['currency'] ?? 'د.ل'}',
            style: GoogleFonts.alexandria(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: isCredit ? AppColors.accentGreen : AppColors.accentRed,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsageChartCard() {
    return GlassCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'استهلاك البيانات الشهري',
            style: GoogleFonts.alexandria(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            height: 200,
            child: Center(
              child: Text(
                'رسم بياني لاستهلاك البيانات',
                style: GoogleFonts.alexandria(
                  fontSize: 14,
                  color: Colors.white.withOpacity(0.7),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: isDark
            ? AppColors.darkSurface.withValues(alpha: 0.95)
            : Colors.white.withValues(alpha: 0.95),
        border: Border(
          top: BorderSide(
            color: isDark
                ? AppColors.borderDark.withValues(alpha: 0.3)
                : AppColors.borderLight.withValues(alpha: 0.3),
            width: 0.5,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        backgroundColor: Colors.transparent,
        elevation: 0,
        selectedItemColor: isDark
            ? AppColors.primaryCyan
            : AppColors.primaryBlue,
        unselectedItemColor: isDark
            ? AppColors.textDarkSecondary
            : AppColors.textSecondary,
        selectedLabelStyle: GoogleFonts.alexandria(
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: GoogleFonts.alexandria(
          fontSize: 12,
          fontWeight: FontWeight.w400,
        ),
        currentIndex: 0,
        onTap: (index) {
          HapticFeedback.lightImpact();
          _handleBottomNavTap(index);
        },
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard_outlined),
            activeIcon: Icon(Icons.dashboard),
            label: 'الرئيسية',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.wifi_outlined),
            activeIcon: Icon(Icons.wifi),
            label: 'الباقات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.receipt_outlined),
            activeIcon: Icon(Icons.receipt),
            label: 'الفواتير',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person_outline),
            activeIcon: Icon(Icons.person),
            label: 'الملف الشخصي',
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: 44,
      height: 44,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: IconButton(
        icon: Icon(
          icon,
          color: Colors.white,
          size: 20,
        ),
        onPressed: onPressed,
        padding: EdgeInsets.zero,
      ),
    );
  }

  void _showNotifications(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'الإشعارات قريباً',
          style: GoogleFonts.alexandria(),
        ),
        backgroundColor: AppColors.primaryBlue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _showProfile(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'الملف الشخصي قريباً',
          style: GoogleFonts.alexandria(),
        ),
        backgroundColor: AppColors.primaryBlue,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _handleBottomNavTap(int index) {
    switch (index) {
      case 0:
        // Already on dashboard
        break;
      case 1:
        Navigator.pushNamed(context, '/user/packages');
        break;
      case 2:
        Navigator.pushNamed(context, '/user/invoices');
        break;
      case 3:
        // Navigate to profile (can be implemented later)
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'الملف الشخصي قريباً',
              style: GoogleFonts.alexandria(),
            ),
            backgroundColor: AppColors.primaryBlue,
          ),
        );
        break;
    }
  }

  String _getUsageText() {
    if (_trafficData.isNotEmpty && _trafficData['data'] != null) {
      final data = _trafficData['data'];

      // Get total usage from arrays
      if (data['total'] != null && data['total'] is List) {
        final totalList = data['total'] as List;
        final totalBytes = totalList.fold<num>(0, (sum, item) => sum + (item ?? 0));

        if (totalBytes > 0) {
          final totalGB = (totalBytes / (1024 * 1024 * 1024)).toStringAsFixed(1);
          return '$totalGB GB';
        }
      }

      // Try rx + tx arrays
      if (data['rx'] != null && data['tx'] != null) {
        final rxList = data['rx'] as List? ?? [];
        final txList = data['tx'] as List? ?? [];

        final totalRx = rxList.fold<num>(0, (sum, item) => sum + (item ?? 0));
        final totalTx = txList.fold<num>(0, (sum, item) => sum + (item ?? 0));
        final totalBytes = totalRx + totalTx;

        if (totalBytes > 0) {
          final totalGB = (totalBytes / (1024 * 1024 * 1024)).toStringAsFixed(1);
          return '$totalGB GB';
        }
      }

      // If no usage, show 0.0 GB instead of "قريباً"
      return '0.0 GB';
    }
    return 'قريباً';
  }

  String _getCurrentPackage(dynamic user) {
    // Return the loaded package name
    if (_currentPackageName != 'غير محدد') {
      return _currentPackageName;
    }

    // Try to get package info from user data
    if (user != null) {
      // Check currentPackage
      if (user.currentPackage != null && user.currentPackage.isNotEmpty && user.currentPackage != 'غير محدد') {
        return user.currentPackage;
      }
    }

    return 'باقة الإنترنت الأساسية';
  }


}