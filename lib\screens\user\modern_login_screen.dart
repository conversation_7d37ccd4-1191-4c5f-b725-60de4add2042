import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../utils/app_colors.dart';
import '../../widgets/modern_card.dart';
import '../../widgets/modern_button.dart';
import '../../widgets/modern_input.dart';

class ModernLoginScreen extends StatefulWidget {
  const ModernLoginScreen({super.key});

  @override
  State<ModernLoginScreen> createState() => _ModernLoginScreenState();
}

class _ModernLoginScreenState extends State<ModernLoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // بدء بحقول فارغة للإنتاج
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);
    HapticFeedback.lightImpact();

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      final success = await authProvider.loginUser(
        _usernameController.text.trim(),
        _passwordController.text,
        'http://192.168.31.200', // Base URL - تجربة HTTP بدلاً من HTTPS
      );

      if (mounted) {
        if (success && authProvider.isAuthenticated) {
          // إظهار رسالة نجاح
          _showSuccessSnackBar('مرحباً ${authProvider.currentUser?.fullName ?? 'بك'}!');

          // انتظار قصير ثم التنقل
          await Future.delayed(const Duration(milliseconds: 500));

          if (mounted) {
            Navigator.pushReplacementNamed(context, '/dashboard');
          }
        } else {
          _showErrorSnackBar(
            authProvider.error ?? 'فشل في تسجيل الدخول. تحقق من البيانات.'
          );
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('حدث خطأ أثناء تسجيل الدخول. تحقق من الاتصال بالخادم.');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: GoogleFonts.alexandria(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: GoogleFonts.alexandria(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      backgroundColor: isDark ? AppColors.darkBackground : AppColors.neutral50,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              const SizedBox(height: 60),
              
              // Logo and Title
              _buildHeader(),
              
              const SizedBox(height: 60),
              
              // Login Form
              _buildLoginForm(),
              
              const SizedBox(height: 40),
              
              // Footer
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Column(
      children: [
        // Logo
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppColors.primaryBlue,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: AppColors.primaryBlue.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: const Icon(
            Icons.wifi,
            color: Colors.white,
            size: 40,
          ),
        ),
        
        const SizedBox(height: 24),
        
        // Title
        Text(
          'مرحباً بك',
          style: GoogleFonts.alexandria(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: isDark ? AppColors.textDarkPrimary : AppColors.textPrimary,
          ),
        ),
        
        const SizedBox(height: 8),
        
        // Subtitle
        Text(
          'سجل دخولك للوصول إلى حسابك',
          style: GoogleFonts.alexandria(
            fontSize: 16,
            color: isDark ? AppColors.textDarkSecondary : AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildLoginForm() {
    return ModernCard(
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            // Username Field
            ModernInput(
              label: 'اسم المستخدم',
              hint: 'أدخل اسم المستخدم',
              controller: _usernameController,
              prefixIcon: Icons.person_outline,
              keyboardType: TextInputType.text,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال اسم المستخدم';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 20),
            
            // Password Field
            ModernInput(
              label: 'كلمة المرور',
              hint: 'أدخل كلمة المرور',
              controller: _passwordController,
              prefixIcon: Icons.lock_outline,
              suffixIcon: _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
              onSuffixIconTap: () {
                setState(() {
                  _isPasswordVisible = !_isPasswordVisible;
                });
              },
              obscureText: !_isPasswordVisible,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'يرجى إدخال كلمة المرور';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 32),
            
            // Login Button
            ModernButton(
              text: 'تسجيل الدخول',
              onPressed: _isLoading ? null : _login,
              isLoading: _isLoading,
              isFullWidth: true,
              height: 52,
              fontSize: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFooter() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Column(
      children: [
        Text(
          'نظام إدارة خدمات الإنترنت',
          style: GoogleFonts.alexandria(
            fontSize: 14,
            color: isDark ? AppColors.textDarkTertiary : AppColors.textTertiary,
          ),
        ),
        
        const SizedBox(height: 8),
        
        Text(
          'الإصدار 1.0.0',
          style: GoogleFonts.alexandria(
            fontSize: 12,
            color: isDark ? AppColors.textDarkTertiary : AppColors.textTertiary,
          ),
        ),
      ],
    );
  }
}
