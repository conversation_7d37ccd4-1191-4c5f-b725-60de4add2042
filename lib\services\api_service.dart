import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../api/user_api.dart';
import '../models/user_model.dart';
import '../models/package_model.dart';
import '../models/invoice_model.dart';
import '../api/user_api.dart';

class ApiService {
  static const String _tokenKey = 'auth_token';
  static const String _userDataKey = 'user_data';

  late final UserApi _userApi;
  final FlutterSecureStorage _storage = const FlutterSecureStorage();

  String? _currentToken;
  UserModel? _currentUser;
  String? _error;
  final String _baseUrl;

  ApiService(String baseUrl) : _baseUrl = baseUrl {
    _userApi = UserApi(baseUrl);
  }
  
  // Getters
  String? get currentToken => _currentToken;
  UserModel? get currentUser => _currentUser;
  String? get error => _error;
  String get baseUrl => _baseUrl;
  bool get isAuthenticated => _currentToken != null;

  // Login method that returns bool (for AuthProvider compatibility)
  Future<bool> loginUser(String username, String password) async {
    try {
      _error = null;
      final response = await _userApi.loginUser(username, password);

      if (response['token'] != null) {
        _currentToken = response['token'];
        await _storage.write(key: _tokenKey, value: _currentToken);

        // Get user details after login
        try {
          final userDetails = await _userApi.getUserDetails();
          // User details received successfully
          _currentUser = UserModel.fromJson(userDetails);
          await _storage.write(key: _userDataKey, value: _currentUser!.toJsonString());
        } catch (e) {
          // Failed to get user details, trying fallback
          // Try to extract user info from login response
          if (response['user'] != null) {
            try {
              _currentUser = UserModel.fromJson(response['user']);
              await _storage.write(key: _userDataKey, value: _currentUser!.toJsonString());
            } catch (parseError) {
              // Failed to parse user from login response, using basic model
              // Create basic user model with available data
              _currentUser = UserModel(
                id: response['user_id'] ?? 1,
                username: username,
                firstName: response['name'] ?? username,
                email: response['email'] ?? '',
                phone: response['phone'] ?? '',
                balance: double.tryParse(response['balance']?.toString() ?? '0') ?? 0.0,
                status: response['status'] ?? 'active',
                currentPackage: response['package'] ?? 'غير محدد',
                createdAt: DateTime.now(),
              );
              await _storage.write(key: _userDataKey, value: _currentUser!.toJsonString());
            }
          } else {
            // Fallback user model
            _currentUser = UserModel(
              id: 1,
              username: username,
              firstName: username,
              email: '',
              phone: '',
              balance: 0.0,
              status: 'active',
              currentPackage: 'غير محدد',
              createdAt: DateTime.now(),
            );
            await _storage.write(key: _userDataKey, value: _currentUser!.toJsonString());
          }
        }

        return true;
      } else {
        _error = 'لم يتم استلام رمز المصادقة';
        return false;
      }
    } catch (e) {
      if (e.toString().contains('SocketException') || e.toString().contains('Connection')) {
        _error = 'لا يمكن الاتصال بالخادم. تحقق من الاتصال بالإنترنت.';
      } else if (e.toString().contains('401') || e.toString().contains('Unauthorized')) {
        _error = 'اسم المستخدم أو كلمة المرور غير صحيحة.';
      } else if (e.toString().contains('403') || e.toString().contains('Forbidden')) {
        _error = 'الحساب غير مفعل أو محظور.';
      } else if (e.toString().contains('500') || e.toString().contains('Internal Server Error')) {
        _error = 'خطأ في الخادم. حاول مرة أخرى لاحقاً.';
      } else {
        _error = 'فشل تسجيل الدخول. تحقق من البيانات والاتصال.';
      }
      return false;
    }
  }
  
  // Authentication
  Future<UserModel> login(String username, String password) async {
    try {
      final response = await _userApi.loginUser(username, password);
      
      if (response['token'] != null) {
        _currentToken = response['token'];
        await _storage.write(key: _tokenKey, value: _currentToken);
        
        // Get user details after login
        final userDetails = await _userApi.getUserDetails();
        _currentUser = UserModel.fromJson(userDetails);
        await _storage.write(key: _userDataKey, value: _currentUser!.toJsonString());
        
        return _currentUser!;
      } else {
        throw Exception('لم يتم استلام رمز المصادقة');
      }
    } catch (e) {
      throw Exception('فشل تسجيل الدخول: ${e.toString()}');
    }
  }
  
  Future<void> logout() async {
    _currentToken = null;
    _currentUser = null;
    await _storage.delete(key: _tokenKey);
    await _storage.delete(key: _userDataKey);
  }
  
  Future<bool> tryAutoLogin() async {
    try {
      final token = await _storage.read(key: _tokenKey);
      final userData = await _storage.read(key: _userDataKey);
      
      if (token != null && userData != null) {
        _currentToken = token;
        _userApi.setToken(token);
        _currentUser = UserModel.fromJsonString(userData);
        
        // Verify token is still valid by making a test request
        await _userApi.getUserDetails();
        return true;
      }
      return false;
    } catch (e) {
      // Token is invalid, clear stored data
      await logout();
      return false;
    }
  }
  
  // User Data
  Future<UserModel> refreshUserData() async {
    try {
      final userDetails = await _userApi.getUserDetails();
      // Raw user details received

      // Handle the API response structure
      Map<String, dynamic> userData;
      if (userDetails['data'] != null) {
        userData = userDetails['data'];
      } else {
        userData = userDetails;
      }

      _currentUser = UserModel.fromJson({'data': userData});
      await _storage.write(key: _userDataKey, value: _currentUser!.toJsonString());
      return _currentUser!;
    } catch (e) {
      throw Exception('فشل في تحديث بيانات المستخدم: ${e.toString()}');
    }
  }
  
  // Packages
  Future<List<PackageModel>> getPackages() async {
    try {
      final response = await _userApi.getPackages();
      return response.map((json) => PackageModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في جلب الباقات: ${e.toString()}');
    }
  }
  
  Future<List<PackageModel>> getServices() async {
    try {
      final response = await _userApi.getServices();
      return response.map((json) => PackageModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في جلب الخدمات: ${e.toString()}');
    }
  }
  
  // Invoices
  Future<List<InvoiceModel>> getInvoices({int page = 1, int count = 10}) async {
    try {
      final response = await _userApi.getInvoices(page: page, count: count);
      final invoicesData = response['data'] as List<dynamic>? ?? [];
      return invoicesData.map((json) => InvoiceModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في جلب الفواتير: ${e.toString()}');
    }
  }
  
  // Traffic Data
  Future<Map<String, dynamic>> getTrafficData({
    String reportType = 'daily',
    int? month,
    int? year,
  }) async {
    try {
      return await _userApi.getTrafficData(
        reportType: reportType,
        month: month,
        year: year,
      );
    } catch (e) {
      throw Exception('فشل في جلب بيانات الاستهلاك: ${e.toString()}');
    }
  }
  
  // Service Management
  Future<bool> changeService(String newServiceId, String currentPassword) async {
    try {
      await _userApi.changeService(newServiceId, currentPassword);
      // Refresh user data after service change
      await refreshUserData();
      return true;
    } catch (e) {
      throw Exception('فشل في تغيير الخدمة: ${e.toString()}');
    }
  }
  
  // Redeem Code
  Future<Map<String, dynamic>> redeemCode(String pin) async {
    try {
      final response = await _userApi.redeemCode(pin);
      // Refresh user data after redeeming code
      await refreshUserData();
      return response;
    } catch (e) {
      throw Exception('فشل في استخدام الكود: ${e.toString()}');
    }
  }
  
  // User Sessions
  Future<Map<String, dynamic>> getUserSessions({int page = 1, int count = 10}) async {
    try {
      return await _userApi.getUserSessions(page: page, count: count);
    } catch (e) {
      throw Exception('فشل في جلب الجلسات: ${e.toString()}');
    }
  }
}
