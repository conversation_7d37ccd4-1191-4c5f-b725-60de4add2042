import 'dart:io';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';

class UserApi {
  final String baseUrl;
  late final Dio _dio;
  String? _token;

  UserApi(this.baseUrl) {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      sendTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'User-Agent': 'ISP-App/1.0',
      },
      validateStatus: (status) {
        return status != null && status < 500;
      },
    ));

    // إعدادات خاصة للخوادم المحلية - تجاهل SSL
    if (baseUrl.contains('192.168.') || baseUrl.contains('localhost') || baseUrl.contains('127.0.0.1')) {
      print('🔧 إعداد Dio للخادم المحلي - تجاهل SSL');
      (_dio.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
        final client = HttpClient();
        client.badCertificateCallback = (cert, host, port) => true;
        return client;
      };
    }

    // Add interceptor for token
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        if (_token != null) {
          options.headers['Authorization'] = 'Bearer $_token';
        }
        handler.next(options);
      },
    ));
  }

  void setToken(String token) {
    _token = token;
  }

  Future<Map<String, dynamic>> loginUser(String username, String password, {String language = 'ar'}) async {
    try {
      print('📡 إرسال طلب تسجيل الدخول إلى: $baseUrl/user/api/index.php/api/auth/login');

      final loginData = {
        'username': username,
        'password': password,
        'language': language
      };
      print('📤 بيانات الطلب: $loginData');

      final response = await _dio.post(
        '/user/api/index.php/api/auth/login',
        data: FormData.fromMap(loginData),
        options: Options(
          headers: {
            'Content-Type': 'multipart/form-data',
            'Accept': 'application/json',
          },
        ),
      );

      print('📥 استجابة الخادم - Status Code: ${response.statusCode}');
      print('📥 استجابة الخادم - Headers: ${response.headers}');
      print('📥 استجابة الخادم - Data: ${response.data}');

      if (response.statusCode == 200 && response.data != null) {
        final data = response.data is String
            ? {'message': response.data, 'success': true}
            : response.data as Map<String, dynamic>;

        // Look for token in different possible fields
        String? token;
        if (data['token'] != null) {
          token = data['token'];
        } else if (data['access_token'] != null) {
          token = data['access_token'];
        } else if (data['auth_token'] != null) {
          token = data['auth_token'];
        }

        if (token != null) {
          setToken(token);
        }

        return data;
      } else {
        throw Exception('استجابة غير صحيحة من الخادم');
      }

    } on DioException catch (e) {
      print('💥 DioException في UserApi.loginUser:');
      print('   - Type: ${e.type}');
      print('   - Message: ${e.message}');
      print('   - Response Status: ${e.response?.statusCode}');
      print('   - Response Data: ${e.response?.data}');
      print('   - Request URL: ${e.requestOptions.uri}');

      String errorMessage = 'فشل تسجيل الدخول';
      if (e.type == DioExceptionType.connectionTimeout) {
        errorMessage = 'انتهت مهلة الاتصال بالخادم';
      } else if (e.type == DioExceptionType.connectionError) {
        errorMessage = 'لا يمكن الاتصال بالخادم. تحقق من الاتصال بالإنترنت';
      } else if (e.type == DioExceptionType.sendTimeout) {
        errorMessage = 'انتهت مهلة إرسال البيانات';
      } else if (e.type == DioExceptionType.receiveTimeout) {
        errorMessage = 'انتهت مهلة استقبال البيانات';
      } else if (e.response?.statusCode == 401) {
        errorMessage = 'اسم المستخدم أو كلمة المرور غير صحيحة';
      } else if (e.response?.statusCode == 404) {
        errorMessage = 'الخدمة غير متوفرة على الخادم';
      } else if (e.response?.statusCode == 405) {
        errorMessage = 'طريقة الطلب غير مدعومة';
      } else if (e.response?.statusCode == 500) {
        errorMessage = 'خطأ في الخادم';
      } else if (e.response?.statusCode != null) {
        errorMessage = 'خطأ في الخادم (${e.response?.statusCode})';
      }

      throw Exception(errorMessage);
    } catch (e) {
      print('💥 خطأ عام في UserApi.loginUser: $e');
      throw Exception('فشل تسجيل الدخول: خطأ غير متوقع - ${e.toString()}');
    }
  }

  Future<Map<String, dynamic>> getUserDetails() async {
    try {
      final response = await _dio.get('/user/api/index.php/api/user');

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('فشل في جلب بيانات المستخدم');
      }
    } on DioException catch (e) {
      throw Exception('فشل في جلب بيانات المستخدم: ${e.message}');
    }
  }



  Future<Map<String, dynamic>> getInvoices({int page = 1, int count = 10}) async {
    try {
      final response = await _dio.post(
        '/user/api/index.php/api/index/invoice',
        data: FormData.fromMap({
          'page': page,
          'count': count,
          'sortBy': 'id',
          'direction': 'desc'
        }),
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('فشل في جلب الفواتير');
      }
    } on DioException catch (e) {
      throw Exception('فشل في جلب الفواتير: ${e.message}');
    }
  }

  Future<Map<String, dynamic>> getUserSessions({int page = 1, int count = 10}) async {
    try {
      final response = await _dio.post(
        '/user/api/index.php/api/index/session',
        data: FormData.fromMap({
          'page': page,
          'count': count,
          'sortBy': 'radacctid',
          'direction': 'desc'
        }),
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('فشل في جلب الجلسات');
      }
    } on DioException catch (e) {
      throw Exception('فشل في جلب الجلسات: ${e.message}');
    }
  }

  Future<Map<String, dynamic>> getTrafficData({
    String reportType = 'daily',
    int? month,
    int? year,
    int? userId,
  }) async {
    try {
      final response = await _dio.post(
        '/user/api/index.php/api/traffic',
        data: FormData.fromMap({
          'report_type': reportType,
          'month': month ?? DateTime.now().month,
          'year': year ?? DateTime.now().year,
          'user_id': userId,
        }),
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('فشل في جلب بيانات الاستهلاك');
      }
    } on DioException catch (e) {

      throw Exception('فشل في جلب بيانات الاستهلاك: ${e.message}');
    }
  }

  Future<Map<String, dynamic>> changeService(String newServiceId, String currentPassword) async {
    try {
      final response = await _dio.post(
        '/user/api/index.php/api/service',
        data: FormData.fromMap({
          'new_service': newServiceId,
          'current_password': currentPassword,
        }),
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('فشل في تغيير الخدمة');
      }
    } on DioException catch (e) {

      throw Exception('فشل في تغيير الخدمة: ${e.message}');
    }
  }

  Future<Map<String, dynamic>> redeemCode(String pin) async {
    try {
      final response = await _dio.post(
        '/user/api/index.php/api/redeem',
        data: FormData.fromMap({
          'pin': pin,
        }),
      );

      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      } else {
        throw Exception('فشل في استخدام الكود');
      }
    } on DioException catch (e) {

      throw Exception('فشل في استخدام الكود: ${e.message}');
    }
  }

  // Get available packages
  Future<List<Map<String, dynamic>>> getPackages() async {
    try {
      // جرب GET أولاً
      var response = await _dio.get('/user/api/index.php/api/packages');

      // إذا فشل GET، جرب POST
      if (response.statusCode == 405) {
        response = await _dio.post(
          '/user/api/index.php/api/packages',
          data: FormData.fromMap({}),
        );
      }

      if (response.statusCode == 200) {
        final data = response.data;
        if (data is Map && data['data'] != null && data['data'] is List) {
          final packages = List<Map<String, dynamic>>.from(data['data']);
          return packages;
        } else if (data is List) {
          final packages = List<Map<String, dynamic>>.from(data);
          return packages;
        }
        return [];
      } else {
        throw Exception('فشل في جلب الباقات');
      }
    } on DioException catch (e) {
      throw Exception('فشل في جلب الباقات: ${e.message}');
    }
  }

  // Get available services
  Future<List<Map<String, dynamic>>> getServices() async {
    try {
      final response = await _dio.post(
        '/user/api/index.php/api/services',
        data: FormData.fromMap({}),
      );

      if (response.statusCode == 200) {
        final data = response.data;
        if (data['data'] != null && data['data'] is List) {
          return List<Map<String, dynamic>>.from(data['data']);
        }
        return [];
      } else {
        throw Exception('فشل في جلب الخدمات');
      }
    } on DioException catch (e) {
      throw Exception('فشل في جلب الخدمات: ${e.message}');
    }
  }
}