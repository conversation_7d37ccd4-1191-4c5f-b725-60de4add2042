import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../widgets/glass_card.dart';
import '../../widgets/custom_button.dart' as custom;
import '../../utils/app_colors.dart';
import '../../providers/auth_provider.dart';
import '../../models/invoice_model.dart';

class UserInvoicesScreen extends StatefulWidget {
  const UserInvoicesScreen({super.key});

  @override
  State<UserInvoicesScreen> createState() => _UserInvoicesScreenState();
}

class _UserInvoicesScreenState extends State<UserInvoicesScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  bool _isLoading = false;
  String? _error;
  List<InvoiceModel> _invoices = [];

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    _fadeController.forward();
    _loadInvoices();
  }

  Future<void> _loadInvoices() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Load invoices from API
      final invoices = await authProvider.apiService.getInvoices();

      if (mounted) {
        setState(() {
          _invoices = invoices;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _invoices = [];
          _error = 'فشل في تحميل الفواتير. تحقق من الاتصال بالإنترنت.';
          _isLoading = false;
        });
      }
    }
  }



  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final pendingInvoices = _invoices.where((inv) => inv.isPending).toList();
    final paidInvoices = _invoices.where((inv) => inv.isPaid).toList();

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: isDark ? AppColors.darkGradient : AppColors.primaryGradient,
        ),
        child: SafeArea(
          child: RefreshIndicator(
            onRefresh: _loadInvoices,
            child: CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
              // Custom App Bar
              SliverAppBar(
                expandedHeight: 120,
                floating: false,
                pinned: true,
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    Navigator.pop(context);
                  },
                ),
                flexibleSpace: FlexibleSpaceBar(
                  title: Text(
                    'الفواتير والمدفوعات',
                    style: GoogleFonts.alexandria(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  centerTitle: true,
                ),
              ),

              // Content
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    FadeTransition(
                      opacity: _fadeAnimation,
                      child: AnimationLimiter(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Summary Card
                            _buildSummaryCard(),

                            const SizedBox(height: 24),

                            // Loading State
                            if (_isLoading)
                              const Center(
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              ),

                            // Error State
                            if (_error != null && !_isLoading)
                              GlassCard(
                                child: Column(
                                  children: [
                                    const Icon(
                                      Icons.error_outline,
                                      size: 48,
                                      color: AppColors.error,
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      _error!,
                                      style: GoogleFonts.alexandria(
                                        fontSize: 16,
                                        color: Colors.white,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                    const SizedBox(height: 16),
                                    custom.CustomButton(
                                      text: 'إعادة المحاولة',
                                      type: custom.ButtonType.primary,
                                      onPressed: _loadInvoices,
                                      height: 40,
                                    ),
                                  ],
                                ),
                              ),

                            // Pending Invoices
                            if (!_isLoading && _error == null && pendingInvoices.isNotEmpty) ...[
                              Text(
                                'الفواتير المستحقة',
                                style: GoogleFonts.alexandria(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 16),
                              ...pendingInvoices.asMap().entries.map((entry) {
                                final index = entry.key;
                                final invoice = entry.value;
                                return AnimationConfiguration.staggeredList(
                                  position: index,
                                  duration: const Duration(milliseconds: 600),
                                  child: SlideAnimation(
                                    verticalOffset: 50.0,
                                    child: FadeInAnimation(
                                      child: _buildInvoiceCard(invoice, true),
                                    ),
                                  ),
                                );
                              }),
                              const SizedBox(height: 24),
                            ],

                            // Paid Invoices
                            if (!_isLoading && _error == null) ...[
                              Text(
                                'الفواتير المدفوعة',
                                style: GoogleFonts.alexandria(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 16),
                              if (paidInvoices.isEmpty)
                                GlassCard(
                                  child: Column(
                                    children: [
                                      const Icon(
                                        Icons.receipt_long,
                                        size: 48,
                                        color: Colors.white54,
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        'لا توجد فواتير مدفوعة',
                                        style: GoogleFonts.alexandria(
                                          fontSize: 16,
                                          color: Colors.white,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                )
                              else
                                ...paidInvoices.asMap().entries.map((entry) {
                                  final index = entry.key;
                                  final invoice = entry.value;
                                  return AnimationConfiguration.staggeredList(
                                    position: index + pendingInvoices.length,
                                    duration: const Duration(milliseconds: 600),
                                    child: SlideAnimation(
                                      verticalOffset: 50.0,
                                      child: FadeInAnimation(
                                        child: _buildInvoiceCard(invoice, false),
                                      ),
                                    ),
                                  );
                                }),
                            ],

                            const SizedBox(height: 32),
                          ],
                        ),
                      ),
                    ),
                  ]),
                ),
              ),
            ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryCard() {
    final totalPending = _invoices
        .where((inv) => inv.isPending)
        .fold(0.0, (sum, inv) => sum + inv.amount);

    final totalPaid = _invoices
        .where((inv) => inv.isPaid)
        .fold(0.0, (sum, inv) => sum + inv.amount);

    return GlassCard(
      child: Column(
        children: [
          Row(
            children: [
              const Icon(
                Icons.account_balance_wallet,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'ملخص الفواتير',
                style: GoogleFonts.alexandria(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          Row(
            children: [
              Expanded(
                child: _buildSummaryItem(
                  'المبلغ المستحق',
                  '${totalPending.toStringAsFixed(2)} د.ل',
                  AppColors.accentRed,
                  Icons.pending_actions,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryItem(
                  'المبلغ المدفوع',
                  '${totalPaid.toStringAsFixed(2)} د.ل',
                  AppColors.accentGreen,
                  Icons.check_circle,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String title, String amount, Color color, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: GoogleFonts.alexandria(
              fontSize: 12,
              color: Colors.white.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            amount,
            style: GoogleFonts.alexandria(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceCard(InvoiceModel invoice, bool isPending) {
    // استخدام حالة الفاتورة الفعلية من الـ model
    final isActuallyPending = invoice.isPending;
    final statusColor = isActuallyPending ? AppColors.accentRed : AppColors.accentGreen;
    final statusIcon = isActuallyPending ? Icons.pending_actions : Icons.check_circle;
    final statusText = invoice.statusText;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: GlassCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    statusIcon,
                    color: statusColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        invoice.description ?? 'فاتورة خدمة الإنترنت',
                        style: GoogleFonts.alexandria(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'رقم الفاتورة: ${invoice.invoiceNumber}',
                        style: GoogleFonts.alexandria(
                          fontSize: 12,
                          color: Colors.white.withValues(alpha: 0.7),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: statusColor.withValues(alpha: 0.5),
                    ),
                  ),
                  child: Text(
                    statusText,
                    style: GoogleFonts.alexandria(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: statusColor,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Details
            Row(
              children: [
                Expanded(
                  child: _buildInvoiceDetail('الباقة', invoice.packageName ?? 'غير محدد'),
                ),
                Expanded(
                  child: _buildInvoiceDetail('الفترة', invoice.period ?? 'غير محدد'),
                ),
              ],
            ),

            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: _buildInvoiceDetail('تاريخ الإصدار', invoice.issueDateDisplay),
                ),
                Expanded(
                  child: _buildInvoiceDetail('تاريخ الاستحقاق', invoice.dueDateDisplay),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Amount and Action
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'المبلغ الإجمالي',
                        style: GoogleFonts.alexandria(
                          fontSize: 12,
                          color: Colors.white.withValues(alpha: 0.7),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        invoice.amountDisplay,
                        style: GoogleFonts.alexandria(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isActuallyPending)
                  custom.CustomButton(
                    text: 'دفع الآن',
                    type: custom.ButtonType.gradient,
                    onPressed: () {
                      HapticFeedback.lightImpact();
                      _showPaymentDialog(invoice);
                    },
                    height: 40,
                    fontSize: 14,
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceDetail(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: GoogleFonts.alexandria(
            fontSize: 12,
            color: Colors.white.withValues(alpha: 0.7),
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: GoogleFonts.alexandria(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.white,
          ),
        ),
      ],
    );
  }

  void _showPaymentDialog(InvoiceModel invoice) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? AppColors.darkCard
              : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'دفع الفاتورة',
            style: GoogleFonts.alexandria(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'تفاصيل الفاتورة:',
                style: GoogleFonts.alexandria(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '${invoice.description ?? 'فاتورة خدمة الإنترنت'}\nالمبلغ: ${invoice.amountDisplay}',
                style: GoogleFonts.alexandria(
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'اختر طريقة الدفع:',
                style: GoogleFonts.alexandria(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'إلغاء',
                style: GoogleFonts.alexandria(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            custom.CustomButton(
              text: 'دفع بالبطاقة',
              type: custom.ButtonType.primary,
              onPressed: () {
                Navigator.of(context).pop();
                // Implement payment logic
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'تم دفع الفاتورة بنجاح',
                      style: GoogleFonts.alexandria(),
                    ),
                    backgroundColor: AppColors.accentGreen,
                  ),
                );
              },
              height: 40,
              fontSize: 14,
            ),
          ],
        );
      },
    );
  }
}