import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../providers/auth_provider.dart';
import '../../utils/app_colors.dart';
import '../../widgets/modern_card.dart';
import '../../widgets/modern_button.dart';

class ModernDashboardScreen extends StatefulWidget {
  const ModernDashboardScreen({super.key});

  @override
  State<ModernDashboardScreen> createState() => _ModernDashboardScreenState();
}

class _ModernDashboardScreenState extends State<ModernDashboardScreen> {
  bool _isLoading = false;
  Map<String, dynamic> _trafficData = {};
  List<Map<String, dynamic>> _recentInvoices = [];
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Load user data if needed
      if (authProvider.currentUser == null) {
        final success = await authProvider.tryAutoLogin();
        if (!success) {
          if (mounted) {
            Navigator.pushReplacementNamed(context, '/login');
          }
          return;
        }
      }

      // Refresh user data to get latest balance
      await authProvider.refreshUserData();

      // Load traffic data (usage statistics)
      try {
        _trafficData = await authProvider.apiService.getTrafficData(
          reportType: 'daily',
          month: DateTime.now().month,
          year: DateTime.now().year,
        );
      } catch (e) {
        print('Failed to load traffic data: $e');
        _trafficData = {};
      }

      // Load recent invoices
      try {
        final invoices = await authProvider.apiService.getInvoices(count: 3);
        _recentInvoices = invoices.map((invoice) => {
          'id': invoice.id,
          'title': 'فاتورة رقم ${invoice.id}',
          'amount': invoice.amount,
          'date': invoice.issueDate,
          'status': invoice.status,
          'type': 'invoice',
        }).toList();
      } catch (e) {
        print('Failed to load invoices: $e');
        _recentInvoices = [];
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _error = e.toString();
        });
      }
    }
  }

  Widget _buildErrorBanner() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.warning.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.warning_rounded,
              color: AppColors.warning,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'خطأ في تحميل البيانات',
                  style: GoogleFonts.alexandria(
                    fontWeight: FontWeight.w600,
                    color: AppColors.warning,
                  ),
                ),
                Text(
                  'تعذر تحميل بعض البيانات. اسحب للأسفل للمحاولة مرة أخرى.',
                  style: GoogleFonts.alexandria(
                    fontSize: 12,
                    color: isDark ? AppColors.textDarkSecondary : AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: isDark ? AppColors.darkBackground : AppColors.neutral50,
        body: SafeArea(
          child: RefreshIndicator(
            onRefresh: _loadDashboardData,
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                // Header
                _buildHeader(),

                const SizedBox(height: 24),

                // Error Banner (if any)
                if (_error != null) ...[
                  _buildErrorBanner(),
                  const SizedBox(height: 16),
                ],

                // Quick Stats
                _buildQuickStats(),

                const SizedBox(height: 24),

                // Quick Actions
                _buildQuickActions(),

                const SizedBox(height: 32),

                // Recent Activity
                _buildRecentActivity(),

                const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ),
        bottomNavigationBar: _buildBottomNavigation(),
      ),
    );
  }

  Widget _buildHeader() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.currentUser;
        
        return Row(
          children: [
            // Avatar
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: AppColors.primaryBlue,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.person,
                color: Colors.white,
                size: 24,
              ),
            ),
            
            const SizedBox(width: 16),
            
            // User Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'مرحباً، ${user?.fullName ?? 'المستخدم'}',
                    style: GoogleFonts.alexandria(
                      fontWeight: FontWeight.bold,
                      color: isDark ? AppColors.textDarkPrimary : AppColors.textPrimary,
                    ),
                  ),
                  Text(
                    'الرصيد: ${user?.balance.toStringAsFixed(2) ?? '0.00'} د.ل',
                    style: GoogleFonts.alexandria(
                      color: AppColors.success,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            
            // Notifications
            Container(
              width: 44,
              height: 44,
              decoration: BoxDecoration(
                color: isDark ? AppColors.darkSurface : Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isDark ? AppColors.darkBorder : AppColors.borderLight,
                ),
              ),
              child: IconButton(
                icon: Icon(
                  Icons.notifications_outlined,
                  color: isDark ? AppColors.textDarkPrimary : AppColors.textPrimary,
                ),
                onPressed: () {
                  // Navigate to notifications
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildQuickStats() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // Calculate daily usage from traffic data
        String dailyUsage = '0 MB';
        String currentSpeed = 'غير متاح';

        if (_trafficData.isNotEmpty && _trafficData['data'] != null) {
          final data = _trafficData['data'];
          if (data is List && data.isNotEmpty) {
            // Get today's data (last entry)
            final todayData = data.last;
            final download = double.tryParse(todayData['download']?.toString() ?? '0') ?? 0;
            final upload = double.tryParse(todayData['upload']?.toString() ?? '0') ?? 0;
            final totalBytes = download + upload;

            if (totalBytes > 1024 * 1024 * 1024) {
              dailyUsage = '${(totalBytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
            } else if (totalBytes > 1024 * 1024) {
              dailyUsage = '${(totalBytes / (1024 * 1024)).toStringAsFixed(0)} MB';
            } else {
              dailyUsage = '${(totalBytes / 1024).toStringAsFixed(0)} KB';
            }
          }
        }

        // Get current package speed (if available)
        final user = authProvider.currentUser;
        if (user?.currentPackage != null && user!.currentPackage!.isNotEmpty) {
          // This would need to be enhanced to get actual speed from package data
          currentSpeed = 'متصل';
        }

        return Row(
          children: [
            Expanded(
              child: _buildStatCard(
                title: 'الاستهلاك اليومي',
                value: dailyUsage,
                icon: Icons.data_usage_rounded,
                color: AppColors.info,
                isLoading: _isLoading,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildStatCard(
                title: 'حالة الاتصال',
                value: currentSpeed,
                icon: Icons.wifi_rounded,
                color: user?.status == 'active' ? AppColors.success : AppColors.warning,
                isLoading: _isLoading,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    bool isLoading = false,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return ModernCard(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 18,
                ),
              ),
              const Spacer(),
              if (isLoading)
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(color),
                  ),
                )
              else
                Icon(
                  Icons.trending_up_rounded,
                  color: AppColors.success,
                  size: 16,
                ),
            ],
          ),
          const SizedBox(height: 12),
          if (isLoading)
            Container(
              width: 60,
              height: 16,
              decoration: BoxDecoration(
                color: isDark ? AppColors.neutral600.withValues(alpha: 0.3) : AppColors.neutral300.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(8),
              ),
            )
          else
            Text(
              value,
              style: GoogleFonts.alexandria(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                color: isDark ? AppColors.textDarkPrimary : AppColors.textPrimary,
              ),
            ),
          const SizedBox(height: 4),
          Text(
            title,
            style: GoogleFonts.alexandria(
              fontSize: 12,
              color: isDark ? AppColors.textDarkSecondary : AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.primaryBlue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.flash_on_rounded,
                color: AppColors.primaryBlue,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              'الإجراءات السريعة',
              style: GoogleFonts.alexandria(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: isDark ? AppColors.textDarkPrimary : AppColors.textPrimary,
              ),
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.success.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.success.withValues(alpha: 0.2),
                ),
              ),
              child: Text(
                '4 خدمات',
                style: GoogleFonts.alexandria(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: AppColors.success,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 20),

        // Quick Actions Grid
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 3.0,
          children: [
            _buildUnifiedActionButton(
              title: 'الباقات المتاحة',
              subtitle: 'اختر باقتك',
              icon: Icons.wifi_rounded,
              color: AppColors.primaryBlue,
              onTap: () => Navigator.pushNamed(context, '/packages'),
            ),
            _buildUnifiedActionButton(
              title: 'الفواتير',
              subtitle: 'تاريخ الدفع',
              icon: Icons.receipt_long_rounded,
              color: AppColors.success,
              onTap: () => Navigator.pushNamed(context, '/invoices'),
            ),
            _buildUnifiedActionButton(
              title: 'الدعم الفني',
              subtitle: 'تواصل معنا',
              icon: Icons.support_agent_rounded,
              color: AppColors.warning,
              onTap: () {
                _showSupportDialog();
              },
            ),
            _buildUnifiedActionButton(
              title: 'الإعدادات',
              subtitle: 'إعدادات التطبيق',
              icon: Icons.settings_rounded,
              color: AppColors.info,
              onTap: () => Navigator.pushNamed(context, '/settings'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildUnifiedActionButton({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [color, color.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        title,
                        style: GoogleFonts.alexandria(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          fontSize: 12,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: GoogleFonts.alexandria(
                          color: Colors.white.withValues(alpha: 0.8),
                          fontSize: 10,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  color: Colors.white.withValues(alpha: 0.8),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRecentActivity() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'النشاط الأخير',
              style: GoogleFonts.alexandria(
                fontWeight: FontWeight.bold,
                color: isDark ? AppColors.textDarkPrimary : AppColors.textPrimary,
              ),
            ),
            const Spacer(),
            if (_isLoading)
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryBlue),
                ),
              ),
          ],
        ),
        const SizedBox(height: 16),
        ModernCard(
          padding: EdgeInsets.zero,
          child: _isLoading
            ? _buildLoadingActivity()
            : _recentInvoices.isEmpty
              ? _buildEmptyActivity()
              : Column(
                  children: _recentInvoices.asMap().entries.map((entry) {
                    final index = entry.key;
                    final invoice = entry.value;
                    final isLast = index == _recentInvoices.length - 1;

                    return _buildActivityItem(
                      title: invoice['title'],
                      subtitle: '${invoice['amount'].toStringAsFixed(2)} د.ل',
                      time: _formatDate(invoice['date']),
                      icon: Icons.receipt_long_rounded,
                      color: invoice['status'] == 'paid' ? AppColors.success : AppColors.warning,
                      showBorder: !isLast,
                    );
                  }).toList(),
                ),
        ),
      ],
    );
  }

  Widget _buildLoadingActivity() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: List.generate(2, (index) =>
          Padding(
            padding: EdgeInsets.only(bottom: index == 1 ? 0 : 16),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.neutral300.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: double.infinity,
                        height: 16,
                        decoration: BoxDecoration(
                          color: AppColors.neutral300.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: 120,
                        height: 12,
                        decoration: BoxDecoration(
                          color: AppColors.neutral300.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyActivity() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Icon(
            Icons.history_rounded,
            size: 48,
            color: isDark ? AppColors.textDarkTertiary : AppColors.textTertiary,
          ),
          const SizedBox(height: 16),
          Text(
            'لا يوجد نشاط حديث',
            style: GoogleFonts.alexandria(
              color: isDark ? AppColors.textDarkSecondary : AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Widget _buildActivityItem({
    required String title,
    required String subtitle,
    required String time,
    required IconData icon,
    required Color color,
    bool showBorder = true,
  }) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: showBorder ? BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: isDark ? AppColors.darkBorder : AppColors.borderLight,
            width: 1,
          ),
        ),
      ) : null,
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.alexandria(
                    fontWeight: FontWeight.w600,
                    color: isDark ? AppColors.textDarkPrimary : AppColors.textPrimary,
                  ),
                ),
                Text(
                  subtitle,
                  style: GoogleFonts.alexandria(
                    color: isDark ? AppColors.textDarkSecondary : AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Text(
            time,
            style: GoogleFonts.alexandria(
              color: isDark ? AppColors.textDarkTertiary : AppColors.textTertiary,
            ),
          ),
        ],
      ),
    );
  }

  void _showSupportDialog() {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          margin: const EdgeInsets.all(20),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: isDark ? AppColors.darkCard : Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.warning.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.support_agent_rounded,
                  color: AppColors.warning,
                  size: 32,
                ),
              ),
              const SizedBox(height: 20),
              Text(
                'الدعم الفني',
                style: GoogleFonts.alexandria(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: isDark ? AppColors.textDarkPrimary : AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                'تواصل معنا للحصول على المساعدة',
                style: GoogleFonts.alexandria(
                  color: isDark ? AppColors.textDarkSecondary : AppColors.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Expanded(
                    child: Container(
                      height: 48,
                      decoration: BoxDecoration(
                        color: isDark ? AppColors.darkCard : Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isDark
                            ? AppColors.neutral600.withValues(alpha: 0.3)
                            : AppColors.neutral300.withValues(alpha: 0.5),
                        ),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(12),
                          onTap: () => Navigator.pop(context),
                          child: Center(
                            child: Text(
                              'إغلاق',
                              style: GoogleFonts.alexandria(
                                fontWeight: FontWeight.w600,
                                color: isDark ? AppColors.textDarkSecondary : AppColors.textSecondary,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Container(
                      height: 48,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [AppColors.warning, AppColors.warning.withValues(alpha: 0.8)],
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(12),
                          onTap: () {
                            Navigator.pop(context);
                            // Add support contact logic here
                          },
                          child: Center(
                            child: Text(
                              'تواصل',
                              style: GoogleFonts.alexandria(
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }



  Widget _buildBottomNavigation() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      decoration: BoxDecoration(
        color: isDark ? AppColors.darkSurface : Colors.white,
        border: Border(
          top: BorderSide(
            color: isDark ? AppColors.darkBorder : AppColors.borderLight,
          ),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(Icons.home, 'الرئيسية', true, () {}),
              _buildNavItem(Icons.wifi, 'الباقات', false, () => Navigator.pushNamed(context, '/packages')),
              _buildNavItem(Icons.receipt_long, 'الفواتير', false, () => Navigator.pushNamed(context, '/invoices')),
              _buildNavItem(Icons.settings, 'الإعدادات', false, () => Navigator.pushNamed(context, '/settings')),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(IconData icon, String label, bool isActive, VoidCallback onTap) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: isActive
                ? AppColors.primaryBlue
                : isDark
                    ? AppColors.textDarkSecondary
                    : AppColors.textSecondary,
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: GoogleFonts.alexandria(
              color: isActive
                  ? AppColors.primaryBlue
                  : isDark
                      ? AppColors.textDarkSecondary
                      : AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }
}
