import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:flutter/services.dart';

class LanguageProvider with ChangeNotifier {
  Locale _locale = const Locale('ar');
  Map<String, String> _localizedStrings = {};

  Locale get locale => _locale;
  Map<String, String> get localizedStrings => _localizedStrings;

  Future<void> loadLanguage(String langCode) async {
    _locale = Locale(langCode);
    final String jsonString = await rootBundle.loadString('assets/translations/$langCode.json');
    Map<String, dynamic> jsonMap = json.decode(jsonString);
    _localizedStrings = jsonMap.map((key, value) => MapEntry(key, value.toString()));
    notifyListeners();
  }

  String translate(String key) {
    return _localizedStrings[key] ?? key;
  }

  Future<void> changeLanguage(String langCode) async {
    await loadLanguage(langCode);
  }

  bool get isArabic => _locale.languageCode == 'ar';
  bool get isEnglish => _locale.languageCode == 'en';
}