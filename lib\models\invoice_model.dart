import 'dart:convert';

enum InvoiceStatus { pending, paid, overdue, cancelled }

class InvoiceModel {
  final int id;
  final String invoiceNumber;
  final double amount;
  final double? paidAmount;
  final InvoiceStatus status;
  final DateTime issueDate;
  final DateTime dueDate;
  final DateTime? paidDate;
  final String? description;
  final String? packageName;
  final String? period;
  final Map<String, dynamic>? additionalData;

  InvoiceModel({
    required this.id,
    required this.invoiceNumber,
    required this.amount,
    this.paidAmount,
    required this.status,
    required this.issueDate,
    required this.dueDate,
    this.paidDate,
    this.description,
    this.packageName,
    this.period,
    this.additionalData,
  });

  double get remainingAmount {
    return amount - (paidAmount ?? 0);
  }

  bool get isPaid => status == InvoiceStatus.paid;
  bool get isPending => status == InvoiceStatus.pending;
  bool get isOverdue => status == InvoiceStatus.overdue || 
      (status == InvoiceStatus.pending && DateTime.now().isAfter(dueDate));

  String get statusText {
    switch (status) {
      case InvoiceStatus.paid:
        return 'مدفوعة';
      case InvoiceStatus.pending:
        return 'مستحقة';
      case InvoiceStatus.overdue:
        return 'متأخرة';
      case InvoiceStatus.cancelled:
        return 'ملغية';
    }
  }

  String get amountDisplay {
    return '${amount.toStringAsFixed(2)} د.ل';
  }

  String get remainingAmountDisplay {
    return '${remainingAmount.toStringAsFixed(2)} د.ل';
  }

  String get issueDateDisplay {
    return '${issueDate.day}/${issueDate.month}/${issueDate.year}';
  }

  String get dueDateDisplay {
    return '${dueDate.day}/${dueDate.month}/${dueDate.year}';
  }

  String? get paidDateDisplay {
    if (paidDate == null) return null;
    return '${paidDate!.day}/${paidDate!.month}/${paidDate!.year}';
  }

  int get daysUntilDue {
    return dueDate.difference(DateTime.now()).inDays;
  }

  static InvoiceStatus _parseStatus(String? statusString) {
    if (statusString == null) return InvoiceStatus.pending;
    
    switch (statusString.toLowerCase()) {
      case 'paid':
      case 'مدفوعة':
        return InvoiceStatus.paid;
      case 'pending':
      case 'مستحقة':
        return InvoiceStatus.pending;
      case 'overdue':
      case 'متأخرة':
        return InvoiceStatus.overdue;
      case 'cancelled':
      case 'ملغية':
        return InvoiceStatus.cancelled;
      default:
        return InvoiceStatus.pending;
    }
  }

  factory InvoiceModel.fromJson(Map<String, dynamic> json) {
    return InvoiceModel(
      id: json['id'] ?? 0,
      invoiceNumber: json['invoice_number'] ?? json['number'] ?? 'INV-${json['id'] ?? 0}',
      amount: double.tryParse(json['amount']?.toString() ?? '0') ?? 0.0,
      paidAmount: double.tryParse(json['paid_amount']?.toString() ?? '0'),
      status: _parseStatus(json['status']),
      issueDate: DateTime.tryParse(json['issue_date'] ?? json['created_at'] ?? '') 
          ?? DateTime.now(),
      dueDate: DateTime.tryParse(json['due_date'] ?? '') 
          ?? DateTime.now().add(const Duration(days: 30)),
      paidDate: json['paid_date'] != null 
          ? DateTime.tryParse(json['paid_date']) 
          : null,
      description: json['description'] ?? json['notes'],
      packageName: json['package_name'] ?? json['service_name'],
      period: json['period'] ?? json['billing_period'],
      additionalData: json['additional_data'] is Map<String, dynamic> 
          ? json['additional_data'] 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'invoice_number': invoiceNumber,
      'amount': amount,
      'paid_amount': paidAmount,
      'status': status.name,
      'issue_date': issueDate.toIso8601String(),
      'due_date': dueDate.toIso8601String(),
      'paid_date': paidDate?.toIso8601String(),
      'description': description,
      'package_name': packageName,
      'period': period,
      'additional_data': additionalData,
    };
  }

  String toJsonString() {
    return json.encode(toJson());
  }

  InvoiceModel copyWith({
    int? id,
    String? invoiceNumber,
    double? amount,
    double? paidAmount,
    InvoiceStatus? status,
    DateTime? issueDate,
    DateTime? dueDate,
    DateTime? paidDate,
    String? description,
    String? packageName,
    String? period,
    Map<String, dynamic>? additionalData,
  }) {
    return InvoiceModel(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      amount: amount ?? this.amount,
      paidAmount: paidAmount ?? this.paidAmount,
      status: status ?? this.status,
      issueDate: issueDate ?? this.issueDate,
      dueDate: dueDate ?? this.dueDate,
      paidDate: paidDate ?? this.paidDate,
      description: description ?? this.description,
      packageName: packageName ?? this.packageName,
      period: period ?? this.period,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  @override
  String toString() {
    return 'InvoiceModel(id: $id, number: $invoiceNumber, amount: $amountDisplay, status: $statusText)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is InvoiceModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
