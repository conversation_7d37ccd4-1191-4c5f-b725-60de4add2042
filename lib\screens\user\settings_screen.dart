import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../widgets/glass_card.dart';
import '../../widgets/custom_button.dart' as custom;
import '../../utils/app_colors.dart';
import '../../providers/auth_provider.dart';
import '../../providers/language_provider.dart';

class UserSettingsScreen extends StatefulWidget {
  const UserSettingsScreen({super.key});

  @override
  State<UserSettingsScreen> createState() => _UserSettingsScreenState();
}

class _UserSettingsScreenState extends State<UserSettingsScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    _fadeController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: isDark ? AppColors.darkGradient : AppColors.primaryGradient,
        ),
        child: SafeArea(
          child: CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              // Custom App Bar
              SliverAppBar(
                expandedHeight: 120,
                floating: false,
                pinned: true,
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    Navigator.pop(context);
                  },
                ),
                flexibleSpace: FlexibleSpaceBar(
                  title: Text(
                    'الإعدادات',
                    style: GoogleFonts.alexandria(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  centerTitle: true,
                ),
              ),

              // Settings Content
              SliverPadding(
                padding: const EdgeInsets.all(16),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    FadeTransition(
                      opacity: _fadeAnimation,
                      child: AnimationLimiter(
                        child: Column(
                          children: [
                            // Profile Section
                            _buildProfileSection(),

                            const SizedBox(height: 16),

                            // Account Settings
                            _buildAccountSettings(),

                            const SizedBox(height: 16),

                            // App Settings
                            _buildAppSettings(),

                            const SizedBox(height: 16),

                            // Support Section
                            _buildSupportSection(),

                            const SizedBox(height: 16),

                            // Logout Button
                            _buildLogoutButton(),

                            const SizedBox(height: 32),
                          ],
                        ),
                      ),
                    ),
                  ]),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileSection() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.currentUser;
        
        return GlassCard(
          child: Column(
            children: [
              // Profile Avatar
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: AppColors.primaryGradient,
                ),
                child: const Icon(
                  Icons.person,
                  color: Colors.white,
                  size: 40,
                ),
              ),
              
              const SizedBox(height: 16),
              
              // User Name
              Text(
                user?.fullName ?? 'المستخدم',
                style: GoogleFonts.alexandria(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              
              const SizedBox(height: 4),
              
              // Username
              Text(
                '@${user?.username ?? 'user'}',
                style: GoogleFonts.alexandria(
                  fontSize: 14,
                  color: Colors.white.withValues(alpha: 0.7),
                ),
              ),
              
              const SizedBox(height: 8),
              
              // Status Badge
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: user?.isActive == true 
                      ? AppColors.success.withValues(alpha: 0.2)
                      : AppColors.error.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: user?.isActive == true 
                        ? AppColors.success.withValues(alpha: 0.5)
                        : AppColors.error.withValues(alpha: 0.5),
                  ),
                ),
                child: Text(
                  user?.isActive == true ? 'نشط' : 'غير نشط',
                  style: GoogleFonts.alexandria(
                    fontSize: 12,
                    color: user?.isActive == true ? AppColors.success : AppColors.error,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAccountSettings() {
    return GlassCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إعدادات الحساب',
            style: GoogleFonts.alexandria(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          
          const SizedBox(height: 16),
          
          _buildSettingItem(
            icon: Icons.person_outline,
            title: 'تعديل الملف الشخصي',
            subtitle: 'تحديث البيانات الشخصية',
            onTap: () {
              // Navigate to profile edit
            },
          ),
          
          _buildSettingItem(
            icon: Icons.lock_outline,
            title: 'تغيير كلمة المرور',
            subtitle: 'تحديث كلمة المرور',
            onTap: () {
              _showChangePasswordDialog();
            },
          ),
          
          _buildSettingItem(
            icon: Icons.notifications,
            title: 'الإشعارات',
            subtitle: 'إدارة إعدادات الإشعارات',
            onTap: () {
              // Navigate to notifications settings
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAppSettings() {
    return GlassCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إعدادات التطبيق',
            style: GoogleFonts.alexandria(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          
          const SizedBox(height: 16),
          
          Consumer<LanguageProvider>(
            builder: (context, languageProvider, child) {
              return _buildSettingItem(
                icon: Icons.language,
                title: 'اللغة',
                subtitle: languageProvider.locale.languageCode == 'ar' ? 'العربية' : 'English',
                trailing: Switch(
                  value: languageProvider.locale.languageCode == 'ar',
                  onChanged: (value) {
                    languageProvider.changeLanguage(value ? 'ar' : 'en');
                  },
                  activeColor: AppColors.accentGreen,
                ),
                onTap: () {
                  languageProvider.changeLanguage(
                    languageProvider.locale.languageCode == 'ar' ? 'en' : 'ar'
                  );
                },
              );
            },
          ),
          
          _buildSettingItem(
            icon: Icons.dark_mode_outlined,
            title: 'الوضع المظلم',
            subtitle: 'تبديل بين الوضع الفاتح والمظلم',
            trailing: Switch(
              value: Theme.of(context).brightness == Brightness.dark,
              onChanged: (value) {
                // Implement theme switching
              },
              activeColor: AppColors.accentGreen,
            ),
            onTap: () {
              // Toggle theme
            },
          ),
          
          _buildSettingItem(
            icon: Icons.security,
            title: 'الأمان والخصوصية',
            subtitle: 'إعدادات الأمان',
            onTap: () {
              // Navigate to security settings
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSupportSection() {
    return GlassCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الدعم والمساعدة',
            style: GoogleFonts.alexandria(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),

          const SizedBox(height: 16),

          _buildSettingItem(
            icon: Icons.help_outline,
            title: 'مركز المساعدة',
            subtitle: 'الأسئلة الشائعة والدعم',
            onTap: () {
              // Navigate to help center
            },
          ),

          _buildSettingItem(
            icon: Icons.contact_support_outlined,
            title: 'اتصل بنا',
            subtitle: 'تواصل مع فريق الدعم',
            onTap: () {
              // Navigate to contact support
            },
          ),

          _buildSettingItem(
            icon: Icons.info_outline,
            title: 'حول التطبيق',
            subtitle: 'معلومات التطبيق والإصدار',
            onTap: () {
              _showAboutDialog();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLogoutButton() {
    return GlassCard(
      child: custom.CustomButton(
        text: 'تسجيل الخروج',
        type: custom.ButtonType.secondary,
        icon: Icons.logout,
        onPressed: () {
          _showLogoutDialog();
        },
        height: 56,
        fontSize: 16,
      ),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required String subtitle,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppColors.primaryCyan.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: AppColors.primaryCyan,
                size: 20,
              ),
            ),

            const SizedBox(width: 12),

            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.alexandria(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: GoogleFonts.alexandria(
                      fontSize: 12,
                      color: Colors.white.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
            ),

            if (trailing != null)
              trailing
            else
              const Icon(
                Icons.arrow_forward_ios,
                color: Colors.white54,
                size: 16,
              ),
          ],
        ),
      ),
    );
  }

  void _showChangePasswordDialog() {
    final currentPasswordController = TextEditingController();
    final newPasswordController = TextEditingController();
    final confirmPasswordController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? AppColors.darkCard
              : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'تغيير كلمة المرور',
            style: GoogleFonts.alexandria(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: currentPasswordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'كلمة المرور الحالية',
                  prefixIcon: Icon(Icons.lock_outline),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: newPasswordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'كلمة المرور الجديدة',
                  prefixIcon: Icon(Icons.lock),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: confirmPasswordController,
                obscureText: true,
                decoration: const InputDecoration(
                  labelText: 'تأكيد كلمة المرور الجديدة',
                  prefixIcon: Icon(Icons.lock),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'إلغاء',
                style: GoogleFonts.alexandria(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            custom.CustomButton(
              text: 'تغيير',
              type: custom.ButtonType.primary,
              onPressed: () {
                // Implement password change logic
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'تم تغيير كلمة المرور بنجاح',
                      style: GoogleFonts.alexandria(),
                    ),
                    backgroundColor: AppColors.accentGreen,
                  ),
                );
              },
              height: 40,
              fontSize: 14,
            ),
          ],
        );
      },
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? AppColors.darkCard
              : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'تسجيل الخروج',
            style: GoogleFonts.alexandria(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'هل أنت متأكد من أنك تريد تسجيل الخروج؟',
            style: GoogleFonts.alexandria(
              fontSize: 14,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'إلغاء',
                style: GoogleFonts.alexandria(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            custom.CustomButton(
              text: 'تسجيل الخروج',
              type: custom.ButtonType.secondary,
              onPressed: () async {
                Navigator.of(context).pop();

                final authProvider = Provider.of<AuthProvider>(context, listen: false);
                await authProvider.logout();

                if (mounted) {
                  Navigator.of(context).pushNamedAndRemoveUntil(
                    '/user/login',
                    (route) => false,
                  );
                }
              },
              height: 40,
              fontSize: 14,
            ),
          ],
        );
      },
    );
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? AppColors.darkCard
              : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'حول التطبيق',
            style: GoogleFonts.alexandria(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'تطبيق إدارة الإنترنت الاحترافي',
                style: GoogleFonts.alexandria(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'الإصدار: 1.0.0',
                style: GoogleFonts.alexandria(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'تطبيق شامل لإدارة خدمات الإنترنت والفواتير مع واجهة عصرية وميزات متقدمة.',
                style: GoogleFonts.alexandria(
                  fontSize: 14,
                ),
              ),
            ],
          ),
          actions: [
            custom.CustomButton(
              text: 'موافق',
              type: custom.ButtonType.primary,
              onPressed: () {
                Navigator.of(context).pop();
              },
              height: 40,
              fontSize: 14,
            ),
          ],
        );
      },
    );
  }
}
