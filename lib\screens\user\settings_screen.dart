import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

import '../../widgets/custom_button.dart' as custom;
import '../../utils/app_colors.dart';
import '../../providers/auth_provider.dart';
import '../../providers/language_provider.dart';

class UserSettingsScreen extends StatefulWidget {
  const UserSettingsScreen({super.key});

  @override
  State<UserSettingsScreen> createState() => _UserSettingsScreenState();
}

class _UserSettingsScreenState extends State<UserSettingsScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    _fadeController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: isDark ? const Color(0xFF0A0E21) : const Color(0xFFF5F7FA),
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isDark ? Colors.white.withValues(alpha: 0.1) : Colors.black.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.arrow_back_ios_rounded,
                color: isDark ? Colors.white : Colors.black87,
                size: 18,
              ),
            ),
            onPressed: () {
              HapticFeedback.lightImpact();
              Navigator.pop(context);
            },
          ),
          title: Text(
            'الإعدادات',
            style: GoogleFonts.alexandria(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          centerTitle: true,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                // Profile Card
                _buildSimpleProfileCard(isDark),

                const SizedBox(height: 24),

                // Settings Sections
                _buildSimpleSettingsSection(
                  title: 'الحساب',
                  icon: Icons.person_outline_rounded,
                  items: [
                    _buildSimpleSettingItem(
                      icon: Icons.notifications_outlined,
                      title: 'الإشعارات',
                      isDark: isDark,
                      onTap: () {},
                    ),
                  ],
                  isDark: isDark,
                ),

                const SizedBox(height: 20),

                _buildSimpleSettingsSection(
                  title: 'التطبيق',
                  icon: Icons.settings_outlined,
                  items: [
                    Consumer<LanguageProvider>(
                      builder: (context, languageProvider, child) {
                        return _buildSimpleSettingItem(
                          icon: Icons.language_rounded,
                          title: 'اللغة',
                          trailing: Text(
                            languageProvider.locale.languageCode == 'ar' ? 'العربية' : 'English',
                            style: GoogleFonts.alexandria(
                              fontSize: 14,
                              color: isDark ? Colors.white70 : Colors.black54,
                            ),
                          ),
                          isDark: isDark,
                          onTap: () {
                            languageProvider.changeLanguage(
                              languageProvider.locale.languageCode == 'ar' ? 'en' : 'ar'
                            );
                          },
                        );
                      },
                    ),
                    _buildSimpleSettingItem(
                      icon: Icons.dark_mode_outlined,
                      title: 'الوضع المظلم',
                      trailing: Switch(
                        value: isDark,
                        onChanged: (value) {
                          // Implement theme switching
                        },
                        activeColor: AppColors.primaryCyan,
                      ),
                      isDark: isDark,
                      onTap: () {},
                    ),
                    _buildSimpleSettingItem(
                      icon: Icons.storage_outlined,
                      title: 'إدارة التخزين',
                      isDark: isDark,
                      onTap: () {},
                    ),
                  ],
                  isDark: isDark,
                ),

                const SizedBox(height: 20),

                _buildSimpleSettingsSection(
                  title: 'الدعم',
                  icon: Icons.help_outline_rounded,
                  items: [
                    _buildSimpleSettingItem(
                      icon: Icons.help_center_outlined,
                      title: 'مركز المساعدة',
                      isDark: isDark,
                      onTap: () {},
                    ),
                    _buildSimpleSettingItem(
                      icon: Icons.contact_support_outlined,
                      title: 'اتصل بنا',
                      isDark: isDark,
                      onTap: () {},
                    ),
                    _buildSimpleSettingItem(
                      icon: Icons.info_outline_rounded,
                      title: 'حول التطبيق',
                      isDark: isDark,
                      onTap: () => _showAboutDialog(),
                    ),
                  ],
                  isDark: isDark,
                ),

                const SizedBox(height: 32),

                // Logout Button
                _buildSimpleLogoutButton(isDark),

                SizedBox(height: MediaQuery.of(context).padding.bottom + 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSimpleProfileCard(bool isDark) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.currentUser;

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: isDark ? Colors.white.withValues(alpha: 0.05) : Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: isDark
                    ? Colors.black.withValues(alpha: 0.3)
                    : Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              // Avatar
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [AppColors.primaryCyan, AppColors.primaryBlue],
                  ),
                ),
                child: const Icon(
                  Icons.person_rounded,
                  color: Colors.white,
                  size: 30,
                ),
              ),

              const SizedBox(width: 16),

              // User Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user?.fullName ?? 'المستخدم',
                      style: GoogleFonts.alexandria(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: isDark ? Colors.white : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '@${user?.username ?? 'user'}',
                      style: GoogleFonts.alexandria(
                        fontSize: 14,
                        color: isDark ? Colors.white70 : Colors.black54,
                      ),
                    ),
                  ],
                ),
              ),

              // Status
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: user?.isActive == true
                      ? AppColors.success.withValues(alpha: 0.1)
                      : AppColors.error.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: user?.isActive == true ? AppColors.success : AppColors.error,
                    width: 1,
                  ),
                ),
                child: Text(
                  user?.isActive == true ? 'نشط' : 'غير نشط',
                  style: GoogleFonts.alexandria(
                    fontSize: 12,
                    color: user?.isActive == true ? AppColors.success : AppColors.error,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSimpleSettingsSection({
    required String title,
    required IconData icon,
    required List<Widget> items,
    required bool isDark,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isDark ? Colors.white.withValues(alpha: 0.05) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.black.withValues(alpha: 0.3)
                : Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: AppColors.primaryCyan,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: GoogleFonts.alexandria(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                ),
              ],
            ),
          ),
          ...items,
        ],
      ),
    );
  }

  Widget _buildSimpleSettingItem({
    required IconData icon,
    required String title,
    required bool isDark,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          if (onTap != null) {
            HapticFeedback.lightImpact();
            onTap();
          }
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          child: Row(
            children: [
              Icon(
                icon,
                color: isDark ? Colors.white70 : Colors.black54,
                size: 22,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  title,
                  style: GoogleFonts.alexandria(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: isDark ? Colors.white : Colors.black87,
                  ),
                ),
              ),
              if (trailing != null)
                trailing
              else
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  color: isDark ? Colors.white38 : Colors.black26,
                  size: 16,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSimpleLogoutButton(bool isDark) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 8),
      decoration: BoxDecoration(
        color: isDark
            ? AppColors.error.withValues(alpha: 0.08)
            : AppColors.error.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.error.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? Colors.black.withValues(alpha: 0.2)
                : AppColors.error.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            HapticFeedback.mediumImpact();
            _showLogoutDialog();
          },
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: AppColors.error.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.logout_rounded,
                    color: AppColors.error,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'تسجيل الخروج',
                  style: GoogleFonts.alexandria(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.error,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }









  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? AppColors.darkCard
              : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'تسجيل الخروج',
            style: GoogleFonts.alexandria(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'هل أنت متأكد من أنك تريد تسجيل الخروج؟',
            style: GoogleFonts.alexandria(
              fontSize: 14,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'إلغاء',
                style: GoogleFonts.alexandria(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            custom.CustomButton(
              text: 'تسجيل الخروج',
              type: custom.ButtonType.secondary,
              onPressed: () async {
                Navigator.of(context).pop();

                final authProvider = Provider.of<AuthProvider>(context, listen: false);
                await authProvider.logout();

                if (mounted) {
                  Navigator.of(context).pushNamedAndRemoveUntil(
                    '/user/login',
                    (route) => false,
                  );
                }
              },
              height: 40,
              fontSize: 14,
            ),
          ],
        );
      },
    );
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? AppColors.darkCard
              : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'حول التطبيق',
            style: GoogleFonts.alexandria(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'تطبيق إدارة الإنترنت الاحترافي',
                style: GoogleFonts.alexandria(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'الإصدار: 1.0.0',
                style: GoogleFonts.alexandria(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'تطبيق شامل لإدارة خدمات الإنترنت والفواتير مع واجهة عصرية وميزات متقدمة.',
                style: GoogleFonts.alexandria(
                  fontSize: 14,
                ),
              ),
            ],
          ),
          actions: [
            custom.CustomButton(
              text: 'موافق',
              type: custom.ButtonType.primary,
              onPressed: () {
                Navigator.of(context).pop();
              },
              height: 40,
              fontSize: 14,
            ),
          ],
        );
      },
    );
  }
}
