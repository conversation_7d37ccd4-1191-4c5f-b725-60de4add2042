import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';

import '../../widgets/custom_button.dart' as custom;
import '../../utils/app_colors.dart';
import '../../providers/auth_provider.dart';

import '../../models/package_model.dart';

class UserPackagesScreen extends StatefulWidget {
  const UserPackagesScreen({Key? key}) : super(key: key);

  @override
  State<UserPackagesScreen> createState() => _UserPackagesScreenState();
}

class _UserPackagesScreenState extends State<UserPackagesScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  int selectedPackageIndex = -1;
  bool _isLoading = false;
  String? _error;
  List<PackageModel> _packages = [];
  String? _currentUserPackage;

  Color _getPackageColor(int index) {
    final colors = [
      AppColors.accentOrange,
      AppColors.accentGreen,
      AppColors.accentPurple,
      AppColors.primaryCyan,
      AppColors.secondaryTeal,
    ];
    return colors[index % colors.length];
  }

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    _fadeController.forward();
    _loadPackages();
  }

  Future<void> _loadPackages() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Load packages from API
      final packages = await authProvider.apiService.getPackages();

      // Get current user package
      final user = authProvider.currentUser;
      _currentUserPackage = user?.currentPackage;

      // Find current package index
      if (_currentUserPackage != null) {
        for (int i = 0; i < packages.length; i++) {
          if (packages[i].id.toString() == _currentUserPackage ||
              packages[i].name == _currentUserPackage) {
            selectedPackageIndex = i;
            break;
          }
        }
      }

      if (mounted) {
        setState(() {
          _packages = packages;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading packages: $e');
      if (mounted) {
        // إضافة بيانات تجريبية في حالة فشل API
        setState(() {
          _packages = _getFallbackPackages();
          _error = 'تم تحميل البيانات التجريبية. تحقق من الاتصال بـ API.';
          _isLoading = false;
        });
      }
    }
  }

  List<PackageModel> _getFallbackPackages() {
    return [
      PackageModel(
        id: 1,
        name: 'باقة الأساسية',
        description: 'باقة إنترنت أساسية للاستخدام اليومي',
        price: 50.0,
        currency: 'د.ل',
        downloadSpeed: 10,
        uploadSpeed: 5,
        dataLimit: 100,
        validityDays: 30,
        isActive: true,
        isPopular: false,
        features: ['سرعة 10 ميجا', 'حجم 100 جيجا', 'دعم فني 24/7'],
      ),
      PackageModel(
        id: 2,
        name: 'باقة المتقدمة',
        description: 'باقة إنترنت متقدمة للعائلات',
        price: 100.0,
        currency: 'د.ل',
        downloadSpeed: 25,
        uploadSpeed: 10,
        dataLimit: 250,
        validityDays: 30,
        isActive: true,
        isPopular: true,
        features: ['سرعة 25 ميجا', 'حجم 250 جيجا', 'دعم فني 24/7', 'واي فاي مجاني'],
      ),
      PackageModel(
        id: 3,
        name: 'باقة البريميوم',
        description: 'باقة إنترنت فائقة السرعة',
        price: 200.0,
        currency: 'د.ل',
        downloadSpeed: 50,
        uploadSpeed: 25,
        dataLimit: null, // غير محدود
        validityDays: 30,
        isActive: true,
        isPopular: false,
        features: ['سرعة 50 ميجا', 'حجم غير محدود', 'دعم فني 24/7', 'واي فاي مجاني', 'IP ثابت'],
      ),
    ];
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? const Color(0xFF0A0E21) : const Color(0xFFF5F7FA),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: isDark ? Colors.white.withValues(alpha: 0.1) : Colors.black.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.arrow_back_ios_rounded,
              color: isDark ? Colors.white : Colors.black87,
              size: 18,
            ),
          ),
          onPressed: () {
            HapticFeedback.lightImpact();
            Navigator.pop(context);
          },
        ),
        title: Text(
          'الباقات المتاحة',
          style: GoogleFonts.alexandria(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
        centerTitle: true,
      ),
      body: RefreshIndicator(
        onRefresh: _loadPackages,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                // Header Card
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppColors.primaryCyan.withValues(alpha: 0.1),
                        AppColors.primaryBlue.withValues(alpha: 0.05),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: AppColors.primaryCyan.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppColors.primaryCyan.withValues(alpha: 0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.wifi_rounded,
                          size: 32,
                          color: AppColors.primaryCyan,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'اختر الباقة المناسبة لك',
                        style: GoogleFonts.alexandria(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: isDark ? Colors.white : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'جميع الباقات تشمل إنترنت ألياف ضوئية عالي السرعة',
                        style: GoogleFonts.alexandria(
                          fontSize: 14,
                          color: isDark ? Colors.white70 : Colors.black54,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // Loading State
                if (_isLoading)
                  Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryCyan),
                    ),
                  ),

                // Error State
                if (_error != null && !_isLoading)
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: isDark ? Colors.white.withValues(alpha: 0.05) : Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: isDark
                              ? Colors.black.withValues(alpha: 0.3)
                              : Colors.black.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        Icon(
                          Icons.error_outline_rounded,
                          size: 48,
                          color: AppColors.error,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _error!,
                          style: GoogleFonts.alexandria(
                            fontSize: 16,
                            color: isDark ? Colors.white : Colors.black87,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        custom.CustomButton(
                          text: 'إعادة المحاولة',
                          type: custom.ButtonType.primary,
                          onPressed: _loadPackages,
                          height: 40,
                        ),
                      ],
                    ),
                  ),

                // Packages List
                if (!_isLoading && _error == null)
                  ..._packages.asMap().entries.map((entry) {
                    final index = entry.key;
                    final package = entry.value;
                    return AnimationConfiguration.staggeredList(
                      position: index,
                      duration: const Duration(milliseconds: 600),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: _buildModernPackageCard(package, index, isDark),
                        ),
                      ),
                    );
                  }),

                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernPackageCard(PackageModel package, int index, bool isDark) {
    final isSelected = index == selectedPackageIndex;
    final isPopular = package.isPopular;
    final packageColor = _getPackageColor(index);

    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      child: Stack(
        children: [
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: isDark ? Colors.white.withValues(alpha: 0.05) : Colors.white,
              borderRadius: BorderRadius.circular(20),
              border: isSelected
                  ? Border.all(color: AppColors.primaryCyan, width: 2)
                  : Border.all(color: isDark ? Colors.white.withValues(alpha: 0.1) : Colors.grey.withValues(alpha: 0.2)),
              boxShadow: [
                BoxShadow(
                  color: isDark
                      ? Colors.black.withValues(alpha: 0.3)
                      : Colors.black.withValues(alpha: 0.1),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
                if (isSelected)
                  BoxShadow(
                    color: AppColors.primaryCyan.withValues(alpha: 0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
              ],
            ),
            child: Column(
              children: [
                // Header Section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        packageColor.withValues(alpha: 0.1),
                        packageColor.withValues(alpha: 0.05),
                      ],
                    ),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  child: Column(
                    children: [
                      // Package Icon and Name
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: packageColor.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: packageColor.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Icon(
                              Icons.wifi_rounded,
                              color: packageColor,
                              size: 28,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  package.name,
                                  style: GoogleFonts.alexandria(
                                    fontSize: 20,
                                    fontWeight: FontWeight.bold,
                                    color: isDark ? Colors.white : Colors.black87,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  package.description ?? '',
                                  style: GoogleFonts.alexandria(
                                    fontSize: 14,
                                    color: isDark ? Colors.white70 : Colors.black54,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 20),

                      // Speed and Data Info
                      Row(
                        children: [
                          Expanded(
                            child: _buildInfoChip(
                              icon: Icons.speed_rounded,
                              label: 'السرعة',
                              value: package.speedDisplay,
                              color: packageColor,
                              isDark: isDark,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: _buildInfoChip(
                              icon: Icons.data_usage_rounded,
                              label: 'البيانات',
                              value: package.dataLimitDisplay,
                              color: packageColor,
                              isDark: isDark,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Content Section
                Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: [
                      // Price Section
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: isDark
                              ? Colors.white.withValues(alpha: 0.05)
                              : Colors.grey.withValues(alpha: 0.05),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: packageColor.withValues(alpha: 0.2),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              package.price.toStringAsFixed(0),
                              style: GoogleFonts.alexandria(
                                fontSize: 36,
                                fontWeight: FontWeight.bold,
                                color: packageColor,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Padding(
                              padding: const EdgeInsets.only(bottom: 4),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    package.currency ?? 'د.ل',
                                    style: GoogleFonts.alexandria(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: isDark ? Colors.white70 : Colors.black54,
                                    ),
                                  ),
                                  Text(
                                    'شهرياً',
                                    style: GoogleFonts.alexandria(
                                      fontSize: 12,
                                      color: isDark ? Colors.white60 : Colors.black45,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),

                      const SizedBox(height: 20),

                      // Features List
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: isDark
                              ? Colors.white.withValues(alpha: 0.03)
                              : Colors.grey.withValues(alpha: 0.03),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'المميزات المتضمنة:',
                              style: GoogleFonts.alexandria(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: isDark ? Colors.white : Colors.black87,
                              ),
                            ),
                            const SizedBox(height: 12),
                            ...package.features.map<Widget>((feature) {
                              return Container(
                                margin: const EdgeInsets.only(bottom: 8),
                                child: Row(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: packageColor.withValues(alpha: 0.1),
                                        shape: BoxShape.circle,
                                      ),
                                      child: Icon(
                                        Icons.check_rounded,
                                        color: packageColor,
                                        size: 16,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Text(
                                        feature,
                                        style: GoogleFonts.alexandria(
                                          fontSize: 14,
                                          color: isDark ? Colors.white.withValues(alpha: 0.8) : Colors.black.withValues(alpha: 0.7),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }),
                          ],
                        ),
                      ),

                      const SizedBox(height: 24),

                      // Action Button
                      SizedBox(
                        width: double.infinity,
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(16),
                            gradient: isSelected
                                ? null
                                : LinearGradient(
                                    colors: [packageColor, packageColor.withValues(alpha: 0.8)],
                                  ),
                          ),
                          child: custom.CustomButton(
                            text: isSelected ? 'الباقة الحالية' : 'اختيار الباقة',
                            type: isSelected
                                ? custom.ButtonType.secondary
                                : custom.ButtonType.primary,
                            onPressed: isSelected ? null : () {
                              HapticFeedback.lightImpact();
                              setState(() {
                                selectedPackageIndex = index;
                              });
                              _showUpgradeDialog(package);
                            },
                            height: 52,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Popular Badge
          if (isPopular)
            Positioned(
              top: 16,
              right: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [AppColors.accentOrange, AppColors.accentOrange.withValues(alpha: 0.8)],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.accentOrange.withValues(alpha: 0.4),
                      blurRadius: 12,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.star_rounded,
                      color: Colors.white,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'الأكثر شعبية',
                      style: GoogleFonts.alexandria(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // Current Package Badge
          if (isSelected)
            Positioned(
              top: 16,
              left: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: AppColors.success,
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.check_circle_rounded,
                      color: AppColors.success,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'الباقة الحالية',
                      style: GoogleFonts.alexandria(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: AppColors.success,
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
    required bool isDark,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isDark
            ? Colors.white.withValues(alpha: 0.05)
            : Colors.white.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: GoogleFonts.alexandria(
              fontSize: 12,
              color: isDark ? Colors.white60 : Colors.black45,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: GoogleFonts.alexandria(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
        ],
      ),
    );
  }



  void _showUpgradeDialog(PackageModel package) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? AppColors.darkCard
              : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'تأكيد تغيير الباقة',
            style: GoogleFonts.alexandria(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'هل تريد تغيير باقتك إلى ${package.name}؟\n\nسيتم تطبيق التغيير في بداية الدورة القادمة.',
            style: GoogleFonts.alexandria(
              fontSize: 14,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'إلغاء',
                style: GoogleFonts.alexandria(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            custom.CustomButton(
              text: 'تأكيد',
              type: custom.ButtonType.primary,
              onPressed: () async {
                Navigator.of(context).pop();
                await _changePackage(package);
              },
              height: 40,
              fontSize: 14,
            ),
          ],
        );
      },
    );
  }

  Future<void> _changePackage(PackageModel package) async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
      );

      // Call API to change service
      await authProvider.apiService.changeService(
        package.id.toString(),
        '', // You might need to ask for password
      );

      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم طلب تغيير الباقة بنجاح',
              style: GoogleFonts.alexandria(),
            ),
            backgroundColor: AppColors.accentGreen,
          ),
        );

        // Reload packages to update current selection
        _loadPackages();
      }
    } catch (e) {
      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في تغيير الباقة: ${e.toString()}',
              style: GoogleFonts.alexandria(),
            ),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }
}