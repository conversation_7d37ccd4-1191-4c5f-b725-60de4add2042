import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import '../../widgets/glass_card.dart';
import '../../widgets/custom_button.dart' as custom;
import '../../utils/app_colors.dart';
import '../../providers/auth_provider.dart';

import '../../models/package_model.dart';

class UserPackagesScreen extends StatefulWidget {
  const UserPackagesScreen({Key? key}) : super(key: key);

  @override
  State<UserPackagesScreen> createState() => _UserPackagesScreenState();
}

class _UserPackagesScreenState extends State<UserPackagesScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  int selectedPackageIndex = -1;
  bool _isLoading = false;
  String? _error;
  List<PackageModel> _packages = [];
  String? _currentUserPackage;

  Color _getPackageColor(int index) {
    final colors = [
      AppColors.accentOrange,
      AppColors.accentGreen,
      AppColors.accentPurple,
      AppColors.primaryCyan,
      AppColors.secondaryTeal,
    ];
    return colors[index % colors.length];
  }

  @override
  void initState() {
    super.initState();
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    _fadeController.forward();
    _loadPackages();
  }

  Future<void> _loadPackages() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Load packages from API
      final packages = await authProvider.apiService.getPackages();

      // Get current user package
      final user = authProvider.currentUser;
      _currentUserPackage = user?.currentPackage;

      // Find current package index
      if (_currentUserPackage != null) {
        for (int i = 0; i < packages.length; i++) {
          if (packages[i].id.toString() == _currentUserPackage ||
              packages[i].name == _currentUserPackage) {
            selectedPackageIndex = i;
            break;
          }
        }
      }

      if (mounted) {
        setState(() {
          _packages = packages;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading packages: $e');
      if (mounted) {
        setState(() {
          _error = 'فشل في تحميل الباقات. تحقق من الاتصال وحاول مرة أخرى.';
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: isDark ? AppColors.darkGradient : AppColors.primaryGradient,
        ),
        child: SafeArea(
          child: RefreshIndicator(
            onRefresh: _loadPackages,
            child: CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                // Custom App Bar
                SliverAppBar(
                  expandedHeight: 120,
                  floating: false,
                  pinned: true,
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  leading: IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () {
                      HapticFeedback.lightImpact();
                      Navigator.pop(context);
                    },
                  ),
                  flexibleSpace: FlexibleSpaceBar(
                    title: Text(
                      'الباقات المتاحة',
                      style: GoogleFonts.alexandria(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    centerTitle: true,
                  ),
                ),

                // Packages Content
                SliverPadding(
                  padding: const EdgeInsets.all(16),
                  sliver: SliverList(
                    delegate: SliverChildListDelegate([
                      FadeTransition(
                        opacity: _fadeAnimation,
                        child: AnimationLimiter(
                          child: Column(
                            children: [
                              // Header Info
                              GlassCard(
                                child: Column(
                                  children: [
                                    const Icon(
                                      Icons.wifi,
                                      size: 48,
                                      color: Colors.white,
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      'اختر الباقة المناسبة لك',
                                      style: GoogleFonts.alexandria(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      'جميع الباقات تشمل إنترنت ألياف ضوئية عالي السرعة',
                                      style: GoogleFonts.alexandria(
                                        fontSize: 14,
                                        color: Colors.white.withValues(alpha: 0.8),
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                              ),

                              const SizedBox(height: 24),

                              // Loading State
                              if (_isLoading)
                                const Center(
                                  child: CircularProgressIndicator(
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                ),

                              // Error State
                              if (_error != null && !_isLoading)
                                GlassCard(
                                  child: Column(
                                    children: [
                                      const Icon(
                                        Icons.error_outline,
                                        size: 48,
                                        color: AppColors.error,
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        _error!,
                                        style: GoogleFonts.alexandria(
                                          fontSize: 16,
                                          color: Colors.white,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      const SizedBox(height: 16),
                                      custom.CustomButton(
                                        text: 'إعادة المحاولة',
                                        type: custom.ButtonType.primary,
                                        onPressed: _loadPackages,
                                        height: 40,
                                      ),
                                    ],
                                  ),
                                ),

                              // Packages List
                              if (!_isLoading && _error == null)
                                ..._packages.asMap().entries.map((entry) {
                                  final index = entry.key;
                                  final package = entry.value;
                                  return AnimationConfiguration.staggeredList(
                                    position: index,
                                    duration: const Duration(milliseconds: 600),
                                    child: SlideAnimation(
                                      verticalOffset: 50.0,
                                      child: FadeInAnimation(
                                        child: _buildPackageCard(package, index),
                                      ),
                                    ),
                                  );
                                }),

                              const SizedBox(height: 32),
                            ],
                          ),
                        ),
                      ),
                    ]),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPackageCard(PackageModel package, int index) {
    final isSelected = index == selectedPackageIndex;
    final isPopular = package.isPopular;
    final packageColor = _getPackageColor(index);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Stack(
        children: [
          GlassCard(
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Package Header
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: packageColor.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: packageColor.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Icon(
                        Icons.wifi,
                        color: packageColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            package.name,
                            style: GoogleFonts.alexandria(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Text(
                                package.speedDisplay,
                                style: GoogleFonts.alexandria(
                                  fontSize: 14,
                                  color: packageColor,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '•',
                                style: GoogleFonts.alexandria(
                                  fontSize: 14,
                                  color: Colors.white.withValues(alpha: 0.5),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                package.dataLimitDisplay,
                                style: GoogleFonts.alexandria(
                                  fontSize: 14,
                                  color: Colors.white.withValues(alpha: 0.8),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Price
                Row(
                  children: [
                    Text(
                      package.price.toStringAsFixed(0),
                      style: GoogleFonts.alexandria(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          package.currency ?? 'د.ل',
                          style: GoogleFonts.alexandria(
                            fontSize: 16,
                            color: Colors.white.withValues(alpha: 0.8),
                          ),
                        ),
                        Text(
                          'شهرياً',
                          style: GoogleFonts.alexandria(
                            fontSize: 12,
                            color: Colors.white.withValues(alpha: 0.6),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // Features
                ...package.features.map<Widget>((feature) {
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        Icon(
                          Icons.check_circle,
                          color: packageColor,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            feature,
                            style: GoogleFonts.alexandria(
                              fontSize: 14,
                              color: Colors.white.withValues(alpha: 0.9),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }),

                const SizedBox(height: 24),

                // Action Button
                SizedBox(
                  width: double.infinity,
                  child: custom.CustomButton(
                    text: isSelected ? 'الباقة الحالية' : 'اختيار الباقة',
                    type: isSelected
                        ? custom.ButtonType.secondary
                        : custom.ButtonType.gradient,
                    onPressed: isSelected ? null : () {
                      HapticFeedback.lightImpact();
                      setState(() {
                        selectedPackageIndex = index;
                      });
                      _showUpgradeDialog(package);
                    },
                    height: 48,
                  ),
                ),
              ],
            ),
          ),

          // Popular Badge
          if (isPopular)
            Positioned(
              top: 16,
              right: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  gradient: AppColors.accentGradient,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.accentOrange.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Text(
                  'الأكثر شعبية',
                  style: GoogleFonts.alexandria(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),

          // Current Package Badge
          if (isSelected)
            Positioned(
              top: 16,
              left: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.accentGreen.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: AppColors.accentGreen.withValues(alpha: 0.5),
                  ),
                ),
                child: Text(
                  'الباقة الحالية',
                  style: GoogleFonts.alexandria(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppColors.accentGreen,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _showUpgradeDialog(PackageModel package) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).brightness == Brightness.dark
              ? AppColors.darkCard
              : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(
            'تأكيد تغيير الباقة',
            style: GoogleFonts.alexandria(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            'هل تريد تغيير باقتك إلى ${package.name}؟\n\nسيتم تطبيق التغيير في بداية الدورة القادمة.',
            style: GoogleFonts.alexandria(
              fontSize: 14,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'إلغاء',
                style: GoogleFonts.alexandria(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ),
            ),
            custom.CustomButton(
              text: 'تأكيد',
              type: custom.ButtonType.primary,
              onPressed: () async {
                Navigator.of(context).pop();
                await _changePackage(package);
              },
              height: 40,
              fontSize: 14,
            ),
          ],
        );
      },
    );
  }

  Future<void> _changePackage(PackageModel package) async {
    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
      );

      // Call API to change service
      await authProvider.apiService.changeService(
        package.id.toString(),
        '', // You might need to ask for password
      );

      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'تم طلب تغيير الباقة بنجاح',
              style: GoogleFonts.alexandria(),
            ),
            backgroundColor: AppColors.accentGreen,
          ),
        );

        // Reload packages to update current selection
        _loadPackages();
      }
    } catch (e) {
      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'فشل في تغيير الباقة: ${e.toString()}',
              style: GoogleFonts.alexandria(),
            ),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }
}